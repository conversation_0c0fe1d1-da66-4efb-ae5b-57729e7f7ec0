class ChatController < ApplicationController
  before_action :require_user

  def index
    # Redirect to unified messages page with chat tab active
    redirect_to messages_path(tab: 'chat')
  end

  # Temporary debugging endpoint to check authentication
  def debug_auth
    render json: {
      current_user: @current_user&.id,
      session_keys: session.keys,
      pseudonym_session: PseudonymSession.find&.record&.user&.id,
      cookies: cookies.signed["_normandy_session"]&.keys
    }
  end
end

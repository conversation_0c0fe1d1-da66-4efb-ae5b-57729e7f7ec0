# frozen_string_literal: true

#
# Copyright (C) 2011 - present Instructure, Inc.
#
# This file is part of Canvas.
#
# Canvas is free software: you can redistribute it and/or modify it under
# the terms of the GNU Affero General Public License as published by the Free
# Software Foundation, version 3 of the License.
#
# Canvas is distributed in the hope that it will be useful, but WITHOUT ANY
# WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS FOR
# A PARTICULAR PURPOSE. See the GNU Affero General Public License for more
# details.
#
# You should have received a copy of the GNU Affero General Public License along
# with this program. If not, see <http://www.gnu.org/licenses/>.
#
class AssignmentStatus < ApplicationRecord
  validates :assignment_id, :user_id, :status, presence: true
  validates :assignment_id, uniqueness: { scope: :user_id }
  validates :status, inclusion: { in: %w[not_started in_progress completed] }
  
  # Class method to get status for a specific assignment and user
  def self.get_status(assignment_id, user_id)
    return 'not_started' unless assignment_id && user_id
    
    status_record = find_by(assignment_id: assignment_id, user_id: user_id)
    status_record&.status || 'not_started'
  end
  
  # Class method to set status for a specific assignment and user
  def self.set_status(assignment_id, user_id, new_status)
    return false unless assignment_id && user_id && %w[not_started in_progress completed].include?(new_status)
    
    Rails.logger.info "Setting assignment status: assignment_id=#{assignment_id}, user_id=#{user_id}, status=#{new_status}"
    
    status_record = find_or_initialize_by(assignment_id: assignment_id, user_id: user_id)
    status_record.status = new_status
    
    if status_record.save
      Rails.logger.info "Successfully saved assignment status: #{status_record.id}"
      status_record
    else
      Rails.logger.error "Failed to save assignment status: #{status_record.errors.full_messages.join(', ')}"
      false
    end
  end
  
  # Helper method to check if assignment was started
  def self.started?(assignment_id, user_id)
    get_status(assignment_id, user_id) != 'not_started'
  end

  def self.get_status_summary(assignment_id, course_id)
    # Get all students in the course
    course = Course.find(course_id)
    students = course.students
    
    # Get all submissions for this assignment
    submissions = Assignment.find(assignment_id).submissions
                           .where(user_id: students.pluck(:id))
                           .where('workflow_state != ?', 'deleted')
    
    # Get in_progress statuses
    in_progress_user_ids = where(assignment_id: assignment_id, status: 'in_progress')
                          .pluck(:user_id)
    
    # Count statuses
    completed_count = submissions.where('submission_type IS NOT NULL AND submission_type != ?', '').count
    in_progress_count = in_progress_user_ids.count
    not_started_count = students.count - completed_count - in_progress_count
    
    {
      not_started: not_started_count,
      in_progress: in_progress_count,
      completed: completed_count,
      total: students.count
    }
  end
  
  def self.get_last_activity(assignment_id, user_id)
    status_record = find_by(assignment_id: assignment_id, user_id: user_id)
    status_record&.updated_at
  end
end

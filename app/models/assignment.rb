# frozen_string_literal: true

#
# Copyright (C) 2023 - present Instructure, Inc.
#
# This file is part of Canvas.
#
# Canvas is free software: you can redistribute it and/or modify it under
# the terms of the GNU Affero General Public License as published by the Free
# Software Foundation, version 3 of the License.
#
# Canvas is distributed in the hope that it will be useful, but WITHOUT ANY
# WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS FOR
# A PARTICULAR PURPOSE. See the GNU Affero General Public License for more
# details.
#
# You should have received a copy of the GNU Affero General Public License along
# with this program. If not, see <http://www.gnu.org/licenses/>.
#

require_dependency 'auditors/assignment'

class Assignment < AbstractAssignment

  has_many :auditor_assignment_records,
           class_name: "Auditors::ActiveRecord::AssignmentRecord",
           dependent: :destroy,
           inverse_of: :assignment,
           foreign_key: :assignment_id

  # Later versions of Rails try to read the attribute when setting an error for that attribute. In order to maintain
  # backwards compatibility with error consumers, create a fake attribute :custom_params so it doesn't error out.
  attr_reader :custom_params
  
  # Allow setting the user who is updating the assignment
  attr_writer :updating_user

  validates :parent_assignment_id, :sub_assignment_tag, absence: true
  validate :unpublish_ok?, if: -> { will_save_change_to_workflow_state?(to: "unpublished") }

  before_save :before_soft_delete, if: -> { will_save_change_to_workflow_state?(to: "deleted") }
  # Moved audit_due_date_change to AssignmentsApiController#audit_assignment_due_date_change

  # Create assignment statuses when assignment is created OR published
  after_create :create_student_statuses_on_creation
  after_update :create_student_statuses, if: :saved_change_to_workflow_state?
  after_create :send_custom_assignment_created_notifications, if: :published?
  after_update :send_custom_assignment_published_notifications, 
              if: :saved_change_to_workflow_state_and_now_published?

  SUB_ASSIGNMENT_SYNC_ATTRIBUTES = %w[workflow_state unlock_at lock_at grading_type].freeze
  after_commit :update_sub_assignments, if: :sync_attributes_changed?

  validates :passing_grade, 
            numericality: { 
              greater_than_or_equal_to: 0,
              less_than_or_equal_to: ->(assignment) { assignment.points_possible || Float::INFINITY }
            }, 
            allow_nil: true

  set_broadcast_policy do |p|
    p.dispatch :assignment_due_date_changed
    p.to do |assignment|
      # everyone who is _not_ covered by an assignment override affecting due_at
      # (the AssignmentOverride records will take care of notifying those users)
      excluded_ids = participants_with_overridden_due_at.to_set(&:id)
      BroadcastPolicies::AssignmentParticipants.new(assignment, excluded_ids).to
    end
    p.whenever do |assignment|
      BroadcastPolicies::AssignmentPolicy.new(assignment)
                                         .should_dispatch_assignment_due_date_changed?
    end
    p.data { course_broadcast_data }

    p.dispatch :assignment_changed
    p.to do |assignment|
      BroadcastPolicies::AssignmentParticipants.new(assignment).to
    end
    p.whenever do |assignment|
      BroadcastPolicies::AssignmentPolicy.new(assignment)
                                         .should_dispatch_assignment_changed?
    end
    p.data { course_broadcast_data }

    p.dispatch :assignment_created
    p.to do |assignment|
      BroadcastPolicies::AssignmentParticipants.new(assignment).to
    end
    p.whenever do |assignment|
      BroadcastPolicies::AssignmentPolicy.new(assignment)
                                         .should_dispatch_assignment_created?
    end
    p.data { course_broadcast_data }
    p.filter_asset_by_recipient do |assignment, user|
      assignment.overridden_for(user, skip_clone: true)
    end

    p.dispatch :submissions_posted
    p.to do |assignment|
      assignment.course.participating_instructors
    end
    p.whenever do |assignment|
      BroadcastPolicies::AssignmentPolicy.new(assignment)
                                         .should_dispatch_submissions_posted?
    end
    p.data do |record|
      if record.posting_params_for_notifications.present?
        record.posting_params_for_notifications.merge(course_broadcast_data)
      else
        course_broadcast_data
      end
    end
  end

  def has_passing_grade?
    passing_grade.present? && passing_grade > 0
  end
    
  def passing_grade_percentage
    return nil unless has_passing_grade? && points_possible&.positive?
    ((passing_grade / points_possible) * 100).round(2)
  end
    
  def grade_passes?(score)
    return true unless has_passing_grade? # No passing grade set
    return false if score.nil?
    score.to_f >= passing_grade.to_f
  end
    
  def passing_status_for_submission(submission)
    return nil unless has_passing_grade?
    return nil unless submission&.score.present?
    grade_passes?(submission.score) ? 'passing' : 'not_passing'
  end
  
  def effective_group_category_id
    group_category_id || discussion_topic&.group_category_id
  end

  def find_checkpoint(sub_assignment_tag)
    sub_assignments.find_by(sub_assignment_tag:)
  end

  include SmartSearchable
  use_smart_search title_column: :title,
                   body_column: :description,
                   index_scope: ->(course) { course.assignments.active },
                   search_scope: ->(course, user) { Assignments::ScopedToUser.new(course, user, course.assignments.active).scope }

  def show_in_search_for_user?(user)
    include_description?(user)
  end

  def checkpoints_parent?
    has_sub_assignments? && context.discussion_checkpoints_enabled?
  end

  def update_from_sub_assignment(changed_attributes)
    return unless changed_attributes.keys.intersect?(SubAssignment::SUB_ASSIGNMENT_SYNC_ATTRIBUTES)

    self.saved_by = :sub_assignment
    updates = changed_attributes.slice(*SubAssignment::SUB_ASSIGNMENT_SYNC_ATTRIBUTES)

    updates.each do |attr, (_old_value, new_value)|
      send(:"#{attr}=", new_value)
    end

    save!
  end

  def has_student_submissions_for_sub_assignments? # rubocop:disable Naming/PredicateName
    return false unless has_sub_assignments?

    sub_assignments.active.any?(&:has_student_submissions?)
  end

  def self.assignment_ids_with_sub_assignment_submissions(assignment_ids)
    Submission
      .active
      .having_submission
      .joins(:assignment)
      .where(assignment_id: SubAssignment.where(parent_assignment_id: assignment_ids))
      .distinct
      .pluck(:parent_assignment_id)
  end

  # AbstractAssignment method with added support for sub_assignment submisssions
  def can_unpublish?
    return true if new_record?
    return @can_unpublish unless @can_unpublish.nil?

    @can_unpublish = !has_student_submissions? && !has_student_submissions_for_sub_assignments?
  end
  attr_writer :can_unpublish

  # AbstractAssignment method with added support for sub_assignment submisssions
  def self.preload_can_unpublish(assignments, assmnt_ids_with_subs = nil)
    return unless assignments.any?

    assignment_ids = assignments.map(&:id)
    assmnt_ids_with_subs ||= (assignment_ids_with_submissions(assignment_ids) + assignment_ids_with_sub_assignment_submissions(assignment_ids)).uniq
    assignments.each { |a| a.can_unpublish = !assmnt_ids_with_subs.include?(a.id) }
  end

  private

  # Send custom notifications when assignment is created and already published
  def send_custom_assignment_created_notifications
    begin
      Rails.logger.info "Queuing custom creation notifications for published assignment #{id}"
      NewAssignmentNotificationJob.delay.process(self)
    rescue => e
      Rails.logger.error "Failed to queue custom assignment creation notifications for assignment #{id}: #{e.message}"
    end
  end

  # Send custom notifications when assignment is published after being unpublished
  def send_custom_assignment_published_notifications
    begin
      Rails.logger.info "Queuing custom publication notifications for assignment #{id}"
      NewAssignmentNotificationJob.delay.process(self)
    rescue => e
      Rails.logger.error "Failed to queue custom assignment publication notifications for assignment #{id}: #{e.message}"
    end
  end

  def saved_change_to_workflow_state_and_now_published?
    saved_change_to_workflow_state? && 
    workflow_state == 'published' && 
    workflow_state_before_last_save != 'published'
  end
  
  # Create assignment statuses immediately upon assignment creation
  def create_student_statuses_on_creation
    Rails.logger.info "Creating assignment statuses for new assignment #{id}"
    
    # Get all students in the course
    student_ids = context.students.pluck(:id)
    
    # Create assignment statuses for all students with 'not_started' status
    student_ids.each do |student_id|
      begin
        AssignmentStatus.find_or_create_by(
          assignment_id: id,
          user_id: student_id
        ) do |status|
          status.status = 'not_started'
        end
        Rails.logger.debug "Created assignment status for assignment #{id}, student #{student_id}"
      rescue => e
        Rails.logger.error "Failed to create assignment status for assignment #{id}, student #{student_id}: #{e.message}"
      end
    end
    
    Rails.logger.info "Finished creating assignment statuses for assignment #{id} (#{student_ids.count} students)"
  end
  
  # Create assignment statuses when published 
  def create_student_statuses
    if workflow_state == 'published' && workflow_state_before_last_save != 'published'
      Rails.logger.info "Assignment #{id} published, ensuring all students have assignment statuses"
      
      # Get all students in the course
      student_ids = context.students.pluck(:id)
      
      # Create assignment statuses for all students 
      student_ids.each do |student_id|
        begin
          AssignmentStatus.find_or_create_by(
            assignment_id: id,
            user_id: student_id
          ) do |status|
            status.status = 'not_started'
          end
        rescue => e
          Rails.logger.error "Failed to create assignment status on publish for assignment #{id}, student #{student_id}: #{e.message}"
        end
      end
    end
  end

  def before_soft_delete
    sub_assignments.destroy_all
  end

  def governs_submittable?
    true
  end

  def update_sub_assignments
    return unless has_sub_assignments?

    changed_attributes = previous_changes.slice(*SUB_ASSIGNMENT_SYNC_ATTRIBUTES)

    sub_assignments.active.each do |checkpoint|
      updates = {}
      changed_attributes.each do |attr, (_, new_value)|
        updates[attr] = new_value if checkpoint.respond_to?(:"#{attr}=")
      end
      next unless updates.any?

      checkpoint.saved_by = :parent_assignment
      checkpoint.update!(updates)
      checkpoint.saved_by = nil
    end
  end

  def sync_attributes_changed?
    previous_changes.keys.intersect?(SUB_ASSIGNMENT_SYNC_ATTRIBUTES)
  end

  def sync_attributes_changes
    previous_changes.slice(*SUB_ASSIGNMENT_SYNC_ATTRIBUTES)
  end

  def unpublish_ok?
    if has_student_submissions? || has_student_submissions_for_sub_assignments?
      errors.add :workflow_state, I18n.t("Can't unpublish if there are student submissions for the assignment or its sub_assignments")
    end
  end
  
  def due_date_changed?
    saved_change_to_due_at? && !@skip_due_date_audit
  end
  
  def audit_due_date_change
    return unless due_date_changed?

    # Use the instance variable @updating_user set by the controller.
    # Fall back to User.system_user or another appropriate default if @updating_user is nil.
    user = @updating_user
    user ||= User.where(id: saved_by).first if respond_to?(:saved_by) && saved_by.is_a?(Integer) # Ensure saved_by is an ID

    unless user
      user = User.system_user
    end
    
    # Record the due date change in the auditor
    Auditors::Assignment.record_due_date_updated(self, user)
  end
end

<%
# Copyright (C) 2013 - present Instructure, Inc.
#
# This file is part of Canvas.
#
# Canvas is free software: you can redistribute it and/or modify it under
# the terms of the GNU Affero General Public License as published by the Free
# Software Foundation, version 3 of the License.
#
# Canvas is distributed in the hope that it will be useful, but WITHOUT ANY
# WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS FOR
# A PARTICULAR PURPOSE. See the GNU Affero General Public License for more
# details.
#
# You should have received a copy of the GNU Affero General Public License along
# with this program. If not, see <http://www.gnu.org/licenses/>.
%>

<!-- Dashboard Sidebar Container - Individual Scrollable Boxes -->
<div style="
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding: 5px;
">

  <%# Customer-brandable dashboard right-sidebar logo %>
  <% if brand_variable("ic-brand-right-sidebar-logo") != "" %>
  <div class="ic-sidebar-logo">
    <%= image_tag(brand_variable("ic-brand-right-sidebar-logo"), :class => "ic-sidebar-logo__image", :alt => "Canvas by Instructure") %>
  </div>
  <% end %>

  <%# Use the legacy to do list for non-students until it works for those roles %>
  <% render_student_to_do = Account.site_admin.feature_enabled?(:render_both_to_do_lists) ? @user&.has_student_enrollment? : !@user&.non_student_enrollment? %>
  <% render_teacher_to_do = Account.site_admin.feature_enabled?(:render_both_to_do_lists) ? @user&.non_student_enrollment? : !(planner_enabled? && render_student_to_do) %>

  <!-- To Do List Box - Only show if there are items -->
  <% if planner_enabled? && render_student_to_do  %>
    <div class="dashboard-sidebar-box">
      <div class="Sidebar__TodoListContainer"></div>
    </div>
  <% end %>

  <% if render_teacher_to_do %>
    <!-- Teacher To Do List Box - Only show if there are items -->
    <%
      presenter = ToDoListPresenter.new(self, @current_user, nil)
      has_todo_items = presenter.any_assignments?
    %>
    <% if has_todo_items %>
      <div class="dashboard-sidebar-box">
        <%= render :partial => 'courses/to_do_list', :locals => {:contexts => nil, :show_legacy_todo_list => true, :additional_title_context => @user&.has_student_enrollment? } %>
      </div>
    <% end %>

    <!-- Coming Up Events Box -->
    <div class="dashboard-sidebar-box">
      <% locals = {
          :title => t('coming_up', "Coming Up"),
          :period => :one_week,
          :show_context => true,
          :upcoming => true
      }
      %>
      <%= render :partial => "shared/event_list", :object => @upcoming_events, :locals => locals %>
    </div>
  <% end %>

  <!-- Recent Feedback Box -->
  <% if @show_recent_feedback %>
    <div class="dashboard-sidebar-box dashboard-sidebar-box--large">
      <%= render :partial => "shared/event_list", :object => @recent_feedback, :locals => {:title => t('recent_feedback', "Recent Feedback"), :period => :two_weeks, :show_context => true, :is_recent_feedback => true} %>
    </div>
  <% end %>

  <!-- Announcements Box - Only show if there are announcements and user is a teacher -->
  <% if ((@global_announcements&.any? && @user&.non_student_enrollment?) || @course_announcements&.any?) %>
    <div class="dashboard-sidebar-box dashboard-sidebar-box--large" style="flex: 1;">
      <%= render :partial => "shared/announcements_list" %>
    </div>
  <% end %>

  <!-- Course Creation and Grades Buttons -->
  <% unless Account.site_admin.feature_enabled?(:instui_header) %>
    <div>
      <% if show_user_create_course_button(@current_user) || (@current_user&.alternate_account_for_course_creation && @domain_root_account&.feature_enabled?(:create_course_subaccount_picker)) %>
        <button type="button"
                id="start_new_course"
                class="element_toggler btn button-sidebar-wide"
                aria-controls="new_course_form"><%= t('start_new_course', 'Start a New Course') %></button>
        <% if @domain_root_account&.feature_enabled?(:create_course_subaccount_picker) %>
          <div id="create_course_modal_container"></div>
        <% else %>
          <%= render :partial => 'shared/new_course_form' %>
        <% end %>
      <% end %>      
    <% end %>

    <a href="<%= grades_path %>" class="Button button-sidebar-wide">
      <%= t('View Progress') %>
    </a>
  </div>
</div>

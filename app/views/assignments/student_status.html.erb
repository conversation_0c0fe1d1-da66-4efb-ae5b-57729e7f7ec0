<%
# Copyright (C) 2011 - present Instructure, Inc.
#
# This file is part of Canvas.
#
# Canvas is free software: you can redistribute it and/or modify it under
# the terms of the GNU Affero General Public License as published by the Free
# Software Foundation, version 3 of the License.
#
# Canvas is distributed in the hope that it will be useful, but WITHOUT ANY
# WARRANTY; without even the implied warranty of MERCHA<PERSON>ABILITY or FITNESS FOR
# A PARTICULAR PURPOSE. See the GNU Affero General Public License for more
# details.
#
# You should have received a copy of the GNU Affero General Public License along
# with this program. If not, see <http://www.gnu.org/licenses/>.
%>
<% css_bundle :student_status %>
<% content_for :page_title, t(:student_status_for_assignment, "Student Status for %{assignment}", assignment: @assignment.title) %>
<div class="student-status-container">
  <div class="status-header">
    <h1 class="status-title">Student Status for <span class="assignment-name"><%= @assignment.title %></span></h1>
    <div class="breadcrumb">
      <%= link_to @context.name, course_path(@context) %> 
    </div>
  </div>
  <!-- Overview Cards -->
  <div class="status-overview-grid">
    <div class="overview-card not-started-card" data-filter="not_started">
      <div class="card-header">
        <h3 id="not-started-count"><%= @status_groups[:not_started].count %></h3>
      </div>
      <p>Not Started</p>
      <div class="card-filter-indicator"></div>
    </div>
    
    <div class="overview-card in-progress-card" data-filter="in_progress">
      <div class="card-header">
        <h3 id="in-progress-count"><%= @status_groups[:in_progress].count %></h3>
      </div>
      <p>In Progress</p>
      <div class="card-filter-indicator"></div>
    </div>
    
    <div class="overview-card completed-card" data-filter="completed">
      <div class="card-header">
        <h3 id="completed-count"><%= @status_groups[:completed].count %></h3>
      </div>
      <p>Completed</p>
      <div class="card-filter-indicator"></div>
    </div>
    
    <div class="overview-card total-card" data-filter="all">
      <div class="card-header">
        <h3 id="total-count"><%= @students.count %></h3>
      </div>
      <p>Total Students</p>
      <div class="card-filter-indicator"></div>
    </div>
  </div>
  <!-- Filter Controls -->
  <div class="filter-controls">
    <div class="filter-section">
      <label class="filter-label">Filter by Status:</label>
      <div class="filter-buttons">
        <button class="filter-btn active" data-filter="all">All Students</button>
        <button class="filter-btn" data-filter="not_started">Not Started</button>
        <button class="filter-btn" data-filter="in_progress">In Progress</button>
        <button class="filter-btn" data-filter="completed">Completed</button>
      </div>
    </div>
    <div class="clear-filters">
      <button class="clear-btn" id="clear-filters">Clear Filters</button>
    </div>
  </div>
  <!-- Main Table Section -->
  <div class="table-section">
    <div class="section-header">
      <h2>Student Progress Details</h2>
      <div class="results-info">
        <span id="showing-count"><%= @students.count %></span> of <span id="total-students"><%= @students.count %></span> students
      </div>
    </div>
    
    <div class="table-wrapper">
      <table class="students-table">
        <thead>
          <tr>
            <th class="name-col">Student</th>
            <th class="status-col">Status</th>
            <th class="grade-col">Grade</th>
            <th class="date-col">Last Activity</th>
            <th class="actions-col">Actions</th>
          </tr>
        </thead>
        <tbody>
          <% @students.each do |student| %>
            <% student_data = @student_statuses[student.id] %>
            <tr class="student-row" data-status="<%= student_data[:status] %>" data-student-id="<%= student.id %>">
              <td class="student-cell">
                <div class="student-name">
                  <%= link_to student.name, course_user_path(@context, student) %>
                </div>
                <div class="student-email"><%= student.email %></div>
              </td>
              <td class="status-cell">
                <div class="status-display">
                  <% case student_data[:status] %>
                  <% when 'not_started' %>
                    <span class="status-pill not-started">
                      <span class="status-dot"></span>
                      Not Started
                    </span>
                  <% when 'in_progress' %>
                    <span class="status-pill in-progress">
                      <span class="status-dot"></span>
                      In Progress
                    </span>
                  <% when 'completed' %>
                    <span class="status-pill completed">
                      <span class="status-dot"></span>
                      Completed
                    </span>
                  <% end %>
                </div>
              </td>
              <td class="grade-cell">
                <% if student_data[:submission]&.grade %>
                  <div class="grade-display">
                    <span class="grade-score"><%= student_data[:submission].grade %></span>
                    <% if @assignment.points_possible %>
                      <span class="grade-total">/ <%= @assignment.points_possible %></span>
                    <% end %>
                  </div>
                <% else %>
                  <span class="no-grade">—</span>
                <% end %>
              </td>
              <td class="date-cell">
                <% if student_data[:status] == 'completed' && student_data[:submission]&.submitted_at %>
                  <div class="date-info completed">
                    <span class="date-label">Submitted</span>
                    <span class="date-value"><%= datetime_string(student_data[:submission].submitted_at) %></span>
                  </div>
                <% elsif student_data[:status] == 'in_progress' && student_data[:last_activity] %>
                  <div class="date-info in-progress">
                    <span class="date-label">Started</span>
                    <span class="date-value"><%= datetime_string(student_data[:last_activity]) %></span>
                  </div>
                <% else %>
                  <span class="no-activity">No activity yet</span>
                <% end %>
              </td>
              <td class="actions-cell">
                <div class="status-override-dropdown" data-student-id="<%= student.id %>" data-current-status="<%= student_data[:status] %>">
                  <button class="override-trigger">
                    <span class="dots-icon">⋯</span>
                  </button>
                  <div class="override-menu">
                    <div class="menu-header">
                      <span>Change Status</span>
                    </div>
                    <button class="status-option" data-status="not_started">
                      <span class="status-dot not-started-dot"></span>
                      <span>Not Started</span>
                    </button>
                    <button class="status-option" data-status="in_progress">
                      <span class="status-dot in-progress-dot"></span>
                      <span>In Progress</span>
                    </button>
                    <button class="status-option" data-status="completed">
                      <span class="status-dot completed-dot"></span>
                      <span>Completed</span>
                    </button>
                  </div>
                </div>
              </td>
            </tr>
          <% end %>
        </tbody>
      </table>
    </div>
  </div>
</div>
<script>
document.addEventListener('DOMContentLoaded', function() {
  // Filter functionality
  const filterButtons = document.querySelectorAll('.filter-btn');
  const overviewCards = document.querySelectorAll('.overview-card');
  const studentRows = document.querySelectorAll('.student-row');
  const showingCount = document.getElementById('showing-count');
  const clearFiltersBtn = document.getElementById('clear-filters');
  
  let currentFilter = 'all';
  
  // Helper function to format status text
  function formatStatusText(status) {
    const statusMap = {
      'not_started': 'Not Started',
      'in_progress': 'In Progress',
      'completed': 'Completed'
    };
    return statusMap[status] || status;
  }
  
  // Update counts display
  function updateCounts() {
    const visibleRows = document.querySelectorAll('.student-row:not(.hidden)');
    showingCount.textContent = visibleRows.length;
  }
  
  // Filter students
  function filterStudents(status) {
    currentFilter = status;
    
    studentRows.forEach(row => {
      if (status === 'all' || row.dataset.status === status) {
        row.classList.remove('hidden');
      } else {
        row.classList.add('hidden');
      }
    });
    
    // Update button states
    filterButtons.forEach(btn => {
      btn.classList.toggle('active', btn.dataset.filter === status);
    });
    
    // Update card states
    overviewCards.forEach(card => {
      card.classList.toggle('filtered', card.dataset.filter === status);
    });
    
    updateCounts();
  }
  
  // click handlers to filter buttons
  filterButtons.forEach(button => {
    button.addEventListener('click', () => {
      filterStudents(button.dataset.filter);
    });
  });
  
  // click handlers to overview cards
  overviewCards.forEach(card => {
    card.addEventListener('click', () => {
      filterStudents(card.dataset.filter);
    });
  });
  
  // Clear filters
  clearFiltersBtn.addEventListener('click', () => {
    filterStudents('all');
  });
  
  // Status override functionality 
  const overrideDropdowns = document.querySelectorAll('.status-override-dropdown');
  
  // Handle dropdown clicks
  overrideDropdowns.forEach(dropdown => {
    const trigger = dropdown.querySelector('.override-trigger');
    const menu = dropdown.querySelector('.override-menu');
    const statusOptions = dropdown.querySelectorAll('.status-option');
    const currentStatus = dropdown.dataset.currentStatus;
    
    // Mark current status as disabled
    statusOptions.forEach(option => {
      if (option.dataset.status === currentStatus) {
        option.classList.add('current');
        option.textContent = option.textContent + ' (current)';
      }
    });
    
    // Toggle dropdown
    trigger.addEventListener('click', (e) => {
      e.stopPropagation();
      
      // Close other dropdowns
      overrideDropdowns.forEach(otherDropdown => {
        if (otherDropdown !== dropdown) {
          otherDropdown.classList.remove('active');
        }
      });
      
      dropdown.classList.toggle('active');
    });
    
    // Handle status selection 
    statusOptions.forEach(option => {
      option.addEventListener('click', (e) => {
        e.stopPropagation();
        
        const newStatus = option.dataset.status;
        if (newStatus === currentStatus) return; // Don't change to same status
        
        const studentId = dropdown.dataset.studentId;
        const studentName = dropdown.closest('.student-row').querySelector('.student-name a').textContent;
        
        updateStudentStatus(studentId, newStatus, studentName);
        dropdown.classList.remove('active');
      });
    });
  });
  
  // Close dropdowns when clicking outside
  document.addEventListener('click', () => {
    overrideDropdowns.forEach(dropdown => {
      dropdown.classList.remove('active');
    });
  });
  
  // Update student status in the UI and send to server
  function updateStudentStatus(studentId, newStatus, studentName) {
    // Store original status in case we need to revert
    const row = document.querySelector(`[data-student-id="${studentId}"]`);
    if (!row) return;
    
    const originalStatus = row.dataset.status;
    
    // Update UI immediately for better user experience
    updateStudentStatusUI(studentId, newStatus);
    
    // Send update to server
    sendStatusUpdate(studentId, newStatus, originalStatus);
    
    // Show notification
    showNotification(`${studentName}'s status changed to ${formatStatusText(newStatus)}`);
  }
  
  // Format current datetime for display
  function getCurrentDateTimeString() {
    const now = new Date();
    return now.toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
  }
  
  // Separate function to handle UI updates only
  function updateStudentStatusUI(studentId, newStatus) {
    const row = document.querySelector(`[data-student-id="${studentId}"]`);
    if (!row) return;
    // Update the row's data attribute
    row.dataset.status = newStatus;
    // Update the status pill
    const statusCell = row.querySelector('.status-cell .status-display');
    const statusPill = createStatusPill(newStatus);
    statusCell.innerHTML = statusPill;
    // Update the last activity column based on the new status
    const dateCell = row.querySelector('.date-cell');
    updateLastActivityDisplay(dateCell, newStatus);
    // Update the override button's data
    const overrideDropdown = row.querySelector('.status-override-dropdown');
    overrideDropdown.dataset.currentStatus = newStatus;
    // Update dropdown options
    const statusOptions = overrideDropdown.querySelectorAll('.status-option');
    statusOptions.forEach(option => {
      option.classList.remove('current');
      option.textContent = option.textContent.replace(' (current)', '');
      if (option.dataset.status === newStatus) {
        option.classList.add('current');
        option.textContent = option.textContent + ' (current)';
      }
    });
    // Update counts
    updateStatusCounts();
    // Re-apply current filter
    filterStudents(currentFilter);
  }
  
  // Update the last activity display based on status
  function updateLastActivityDisplay(dateCell, newStatus) {
    const currentDateTime = getCurrentDateTimeString();
    const statusText = formatStatusText(newStatus);
    
    switch(newStatus) {
      case 'completed':
        dateCell.innerHTML = `
          <div class="date-info completed">
            <span class="date-label">Status updated to ${statusText}</span>
            <span class="date-value">${currentDateTime}</span>
          </div>
        `;
        break;
      case 'in_progress':
        dateCell.innerHTML = `
          <div class="date-info in-progress">
            <span class="date-label">Status updated to ${statusText}</span>
            <span class="date-value">${currentDateTime}</span>
          </div>
        `;
        break;
      case 'not_started':
        dateCell.innerHTML = '<span class="no-activity">No activity yet</span>';
        break;
      default:
        dateCell.innerHTML = '<span class="no-activity">No activity yet</span>';
    }
  }
  
  // Send status update to server
  function sendStatusUpdate(studentId, newStatus, originalStatus) {
    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || 
                     document.querySelector('[name="csrf-token"]')?.content;
    
    if (!csrfToken) {
      showNotification('Security token missing. Please refresh the page.', 'error');
      return;
    }
    // Build the URL more explicitly
    const courseId = '<%= @context.id %>';
    const assignmentId = '<%= @assignment.id %>';
    const url = `/courses/${courseId}/assignments/${assignmentId}/update_student_status`;
    
    const payload = {
      student_id: studentId,
      status: newStatus
    };
    fetch(url, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': csrfToken,
        'Accept': 'application/json'
      },
      body: JSON.stringify(payload)
    })
    .then(response => {
      if (!response.ok) {
        return response.text().then(text => {
          throw new Error(`HTTP error! status: ${response.status}, body: ${text}`);
        });
      }
      return response.json();
    })
    .then(data => {
      if (data.success) {
        showNotification(`Status successfully updated to ${formatStatusText(newStatus)}`);
      } else {
        showNotification('Failed to update status: ' + (data.error || 'Unknown error'), 'error');
        // Revert the UI changes if server update failed
        if (originalStatus) {
          updateStudentStatusUI(studentId, originalStatus);
        }
      }
    })
    .catch(error => {
      showNotification('Network error: ' + error.message, 'error');
      // Revert the UI changes if there was a network error
      if (originalStatus) {
        updateStudentStatusUI(studentId, originalStatus);
      }
    });
  }
  
  // Create status pill HTML
  function createStatusPill(status) {
    const statusConfig = {
      'not_started': { class: 'not-started', text: 'Not Started' },
      'in_progress': { class: 'in-progress', text: 'In Progress' },
      'completed': { class: 'completed', text: 'Completed' }
    };
    
    const config = statusConfig[status];
    return `
      <span class="status-pill ${config.class}">
        <span class="status-dot"></span>
        ${config.text}
      </span>
    `;
  }
  
  // Update status counts in overview cards
  function updateStatusCounts() {
    const counts = {
      not_started: 0,
      in_progress: 0,
      completed: 0,
      total: studentRows.length
    };
    
    studentRows.forEach(row => {
      const status = row.dataset.status;
      if (counts[status] !== undefined) {
        counts[status]++;
      }
    });
    
    document.getElementById('not-started-count').textContent = counts.not_started;
    document.getElementById('in-progress-count').textContent = counts.in_progress;
    document.getElementById('completed-count').textContent = counts.completed;
    document.getElementById('total-count').textContent = counts.total;
  }
  
  // Show notification using Canvas flash system
  function showNotification(message, type = 'success') {
    // Use Canvas's flash notification system
    if (typeof $.flashMessage === 'function') {
      $.flashMessage(message);
    } else if (typeof $ !== 'undefined' && $.fn.flashMessage) {
      $(document).flashMessage(message);
    } else {
      // Fallback to custom notification if Canvas flash is not available
      const notification = document.createElement('div');
      const bgColor = type === 'error' ? '#dc3545' : '#28a745';
      
      notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${bgColor};
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 4px;
        z-index: 1001;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        font-size: 0.9rem;
        font-weight: 500;
        max-width: 300px;
        word-wrap: break-word;
      `;
      notification.textContent = message;
      
      document.body.appendChild(notification);
      
      // Remove after 5 seconds for errors, 3 seconds for success
      setTimeout(() => {
        notification.remove();
      }, type === 'error' ? 5000 : 3000);
    }
  }
  
  // Keyboard shortcuts
  document.addEventListener('keydown', (e) => {
    // Number keys for quick filtering
    if (e.ctrlKey || e.metaKey) {
      switch(e.key) {
        case '1':
          e.preventDefault();
          filterStudents('all');
          break;
        case '2':
          e.preventDefault();
          filterStudents('not_started');
          break;
        case '3':
          e.preventDefault();
          filterStudents('in_progress');
          break;
        case '4':
          e.preventDefault();
          filterStudents('completed');
          break;
      }
    }
  });
  
  // Initialize
  updateCounts();
});
</script>

<%
# Copyright (C) 2011 - present Instructure, Inc.
#
# This file is part of Canvas.
#
# Canvas is free software: you can redistribute it and/or modify it under
# the terms of the GNU Affero General Public License as published by the Free
# Software Foundation, version 3 of the License.
#
# Canvas is distributed in the hope that it will be useful, but WITHOUT ANY
# WARRANTY; without even the implied warranty of MERCHA<PERSON><PERSON>ILITY or FITNESS FOR
# A PARTICULAR PURPOSE. See the GNU Affero General Public License for more
# details.
#
# You should have received a copy of the GNU Affero General Public License along
# with this program. If not, see <http://www.gnu.org/licenses/>.
%>

<div id="sidebar_content">
  <% if @can_view_grades || @can_grade %>
    <%= render :partial => "grade_assignment" %>
  <% end %>

    <%= render :partial => "submission_sidebar" %>

  <% if can_do(@context, @current_user, :view_all_grades) &&
        ConditionalRelease::Service.triggers_mastery_paths?(@assignment, @current_user, session) %>
    <div id="crs-graphs"></div>
  <% end %>
</div>

<%
# Copyright (C) 2011 - present Instructure, Inc.
#
# This file is part of Canvas.
#
# Canvas is free software: you can redistribute it and/or modify it under
# the terms of the GNU Affero General Public License as published by the Free
# Software Foundation, version 3 of the License.
#
# Canvas is distributed in the hope that it will be useful, but WITHOUT ANY
# WARRANTY; without even the implied warranty of MERCHANT<PERSON>ILITY or FITNESS FOR
# A PARTICULAR PURPOSE. See the GNU Affero General Public License for more
# details.
#
# You should have received a copy of the GNU Affero General Public License along
# with this program. If not, see <http://www.gnu.org/licenses/>.
%>
<ul class='page-action-list'>
  <h2><%= t 'titles.related_items', "Related Items" %></h2>
  <% if @assignment_presenter.can_view_speed_grader_link?(@current_user) %>
    <li id="assignment-speedgrader-link">
      <div id="student_group_filter_mount_point"></div>
      <div id="speed_grader_link_mount_point"></div>
    </li>
  <% end %>

  <%# Student Status Link %>
  <% if can_do(@context, @current_user, :view_all_grades) %>
    <li>
      <a href="/courses/<%= @context.id %>/assignments/<%= @assignment.id %>/student_status" 
         class="icon-stats"><%= t 'links.view_student_status', "View Student Status" %></a>
    </li>
  <% end %>

  <% if @current_student_submissions.present? %>
    <% if @assignment.submission_types && @assignment.submission_types.match(/(online_upload|online_text_entry|online_url)/) && @downloadable_submissions %>
      <li>
        <%= render :partial => 'submissions/submission_download' %>
        <a href="<%= context_url(@context, :context_assignment_submissions_url, @assignment.id, :zip => 1) %>"
          id="download_submission_button"
          class="download_submissions_link icon-download"><%= t 'links.download_submissions', "Download Submissions" %></a>

        <% if @can_grade %>
          <a href="#" class="upload_submissions_link icon-upload" style="<%= hidden unless @assignment.submissions_downloaded? %>"><%= t 'links.reupload_submissions', "Re-Upload Submissions" %></a>
          <%= form_tag context_url(@context, :submissions_upload_context_gradebook_url, @assignment.id), {:id => "re_upload_submissions_form", :multipart => true, :style => "margin-top: 10px; margin-bottom: 0px; display: none;"} do %>
            <div style="font-size: 0.8em;">
              <%= mt <<-EOT
  If you made changes to the student submission files you downloaded
  before, just zip them back up and upload the zip with the form below.
  Students will see the modified files in their comments for the submission.

  *Make sure you don't change the names of the submission files so we can recognize them.*
                EOT
              %>
            </div>
            <div>
              <button id="choose_file_button" class="btn"><%= t 'buttons.choose_file', "Choose File" %></button>
              <div id="uploaded_file_tag"></div>
              <div class="button-container">
                <button id="reuploaded_submissions_button" type="submit" class="btn btn-primary" style="display: none;"><%= t 'buttons.upload_files', "Re-Upload Files" %></button>
              </div>
              <input type="file" name="submissions_zip" style="height:0;width:0;overflow:hidden;"/>
            </div>
          <% end %>
        <% end %>
      </li>
    <% end %>
  <% end %>

  <% if @can_grade && @assignment.submission_reupload_progress.present? %>
    <li>
      <a href="<%= context_url(@context, :show_submissions_upload_context_gradebook_url, @assignment) %>" class="icon-clock"><%= t "View Uploads Status" %></a>
    </li>
  <% end %>

  <% if @can_grade && @assignment.has_peer_reviews? %>
    <li>
      <a href="<%= context_url(@context, :context_assignment_peer_reviews_url, @assignment.id) %>"
        class="assignment_peer_reviews_link icon-peer-review"><%= t 'links.peer_reviews', "Peer Reviews" %></a>
    </li>
  <% end %>
</ul>

<% if @can_grade && @current_student_submissions.present? %>

  <% subs = @current_student_submissions %>
  <div style="margin-top: 10px;">
    <% graded = subs.select(&:graded?).length # this includes resubmissions that were previously graded %>
    <% resubmitted = subs.select(&:needs_regrading?).length %>
    <% submitted = subs.length %>
    <span class="graded_count" id="ratio_of_submissions_graded" style="<%= 'font-weight: bold;' if graded < submitted %>">
      <%= t :graded_count, "%{graded_count} *out of* %{total} Submissions Graded",
            :graded_count => graded, :total => submitted,
            :wrapper => '<span style="font-size: 0.8em;">\1</span>' %>
    </span>
    <% if resubmitted > 0 %>
      <b><br /><%= t :resubmission_count, { :one => "1 Ungraded Re-submission", :other => "%{count} Ungraded Re-submissions" }, :count => resubmitted %></b>
    <% end %>
  </div>

<% end %>

<%
  # Copyright (C) 2020 - present Instructure, Inc.
  #
  # This file is part of Canvas.
  #
  # Canvas is free software: you can redistribute it and/or modify it under
  # the terms of the GNU Affero General Public License as published by the Free
  # Software Foundation, version 3 of the License.
  #
  # Canvas is distributed in the hope that it will be useful, but WITHOUT ANY
  # WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS FOR
  # A PARTICULAR PURPOSE. See the GNU Affero General Public License for more
  # details.
  #
  # You should have received a copy of the GNU Affero General Public License along
  # with this program. If not, see <http://www.gnu.org/licenses/>.
%>

<ul class='student-assignment-overview'>
  <li>
    <span class='title'><%= t :due_at, 'Due' %></span>
    <span class='value'>
          <span class="date_text">
            <% if @assignment.multiple_due_dates? %>
              <%= multiple_due_date_tooltip(@assignment, @current_user) %>
            <% elsif @assignment.due_at %>
              <% ot(:date_time_by, "%{date} by %{time}", :date => capture { %>
                <span class="display_date"><%= date_string(@assignment.due_at) rescue '' %></span><% }, :time => capture { %>
                <span class="display_time"><%= time_string(@assignment.due_at) rescue '' %></span><% }) %>
            <% else %>
              <%= t :no_due_date, 'No Due Date' %>
            <% end %>
          </span><!--
        --></span>
  </li>

  <%# STATUS PROGRESS INDICATOR %>
  <% if @assignment.expects_submission? %>
    <li>
      <span class='title'><%= t :status, 'Status' %></span>
      <span class='value'>
        <% 
          # Get status from database
          progress_status = AssignmentStatus.get_status(@assignment.id, @current_user.id)
          
          # Determine display status
          if @current_user_submission&.has_submission?
            status = 'completed'
            status_text = t(:completed, 'Completed')
            status_icon = 'icon-check'
            status_class = 'status-completed'
          elsif progress_status == 'in_progress'
            status = 'in_progress'
            status_text = t(:in_progress, 'In Progress')
            status_icon = 'icon-clock'
            status_class = 'status-in-progress'
          else
            status = 'not_started'
            status_text = t(:not_started, 'Not Started')
            status_icon = 'icon-assignment'
            status_class = 'status-not-started'
          end
        %>
        <span class="assignment-status <%= status_class %>" data-status="<%= status %>">
          <i class="<%= status_icon %>" aria-hidden="true"></i>
          <%= status_text %>
        </span>
      </span>
    </li>
  <% end %>

  <% if !@assignment.restrict_quantitative_data?(@current_user) %>
  <li>
    <span class='title'><%= t :points, 'Points' %></span>
    <span class='value'><%= n(round_if_whole(@assignment.points_possible)) || t(:no_points, 'None') %></span>
  </li>
  
  <%# PASSING GRADE DISPLAY %>
  <% if @assignment.has_passing_grade? %>
    <li>
      <span class='title'><%= t :passing_grade, 'Passing Grade' %></span>
      <span class='value'>
        <span style="color: #033f1d;">
          <%= n(round_if_whole(@assignment.passing_grade)) %> / <%= n(round_if_whole(@assignment.points_possible)) %> points
          (<%= @assignment.passing_grade_percentage %>%)
        </span>
      </span>
    </li>
  <% end %>
  <% end %>
  
  <% if @assignment.expects_submission? || @assignment.expects_external_submission? %>
    <% turnitin_text = turnitin_active? ? ' ' + t(:turnitin_enabled_short, '(Turnitin enabled)') : '' %>
    <% vericite_text = vericite_active? ? ' ' + t(:vericite_enabled_short, '(VeriCite enabled)') : '' %>
    <li>
      <span class='title'><%= t :submitting, 'Submitting' %></span>
      <span class='value'><%= @assignment.readable_submission_types + turnitin_text %></span>
    </li>
    <% if @assignment.submission_types =~ /online_upload/ && !@assignment.allowed_extensions.blank? -%>
      <li>
        <span class='title'><%= t :submission_types, 'File Types' %></span>
        <span class='value'><%= @assignment.allowed_extensions.to_sentence %></span>
      </li>
    <% end %>
  <% end %>
  
  <% if @assignment.allowed_attempts&.> 0 %>
    <li>
      <span class='title'><%= t :attempts, 'Attempts' %></span>
      <span class='value'><%= @current_user_submission&.attempt || 0 %></span>
    </li>
    <li>
      <span class='title'><%= t :allowed_attempts, 'Allowed Attempts' %></span>
      <span class='value'><%= @assignment.allowed_attempts + (@current_user_submission&.extra_attempts || 0) %></span>
    </li>
  <% end %>
  
  <%= render :partial => "shared/available_dates", :locals => {:association => @assignment} %>
  <div class="clear"></div>
</ul>

<style>
  .assignment-status {
    display: inline-flex;
    align-items: center;
    font-weight: 500;
  }
  
  .assignment-status i {
    margin-right: 5px;
  }
  
  .status-not-started {
    color: #D2021F;
  }
  
  .status-in-progress {
    color: #E19900;
  }
  
  .status-completed {
    color: #00AC18;
  }
</style>

<script>
// Clean, simple assignment status tracking
document.addEventListener('DOMContentLoaded', function() {
  function markAssignmentStarted(assignmentId) {
    var statusElement = document.querySelector('.assignment-status');
    
    // Only mark as started if current status is 'not_started'
    if (statusElement && statusElement.getAttribute('data-status') === 'not_started') {
      console.log('Marking assignment as started:', assignmentId);
      
      // Get CSRF token
      const csrfToken = document.querySelector('meta[name="csrf-token"]');
      if (!csrfToken) {
        console.error('CSRF token not found');
        return;
      }
      
      fetch('/courses/<%= @context.id %>/assignments/<%= @assignment.id %>/mark_started', {
        method: 'POST',
        headers: {
          'Accept': 'application/json',
          'X-CSRF-Token': csrfToken.getAttribute('content'),
          'X-Requested-With': 'XMLHttpRequest'
        }
      })
      .then(response => {
        console.log('Response status:', response.status);
        console.log('Response content-type:', response.headers.get('content-type'));
        
        if (response.ok) {
          // Check if response is JSON
          const contentType = response.headers.get('content-type');
          if (contentType && contentType.includes('application/json')) {
            return response.json();
          } else {
            // If not JSON, just treat as success if status is ok
            console.log('Response is not JSON, but status is OK');
            return { status: 'success' };
          }
        } else {
          throw new Error('HTTP error! status: ' + response.status);
        }
      })
      .then(data => {
        if (data.status === 'success') {
          // Update status display
          statusElement.className = 'assignment-status status-in-progress';
          statusElement.setAttribute('data-status', 'in_progress');
          statusElement.innerHTML = '<i class="icon-clock" aria-hidden="true"></i><%= t(:in_progress, 'In Progress') %>';
          console.log('Assignment status updated to in_progress');
        }
      })
      .catch(error => {
        console.log('Assignment status tracking failed silently:', error);
        // Fail silently - not critical functionality
      });
    }
  }

  // Listen for submission button clicks
  var submitButton = document.querySelector('.submit_assignment_link');
  if (submitButton) {
    submitButton.addEventListener('click', function() {
      markAssignmentStarted(<%= @assignment.id %>);
    });
  }

  // Listen for when submission modal opens
  var submitModal = document.getElementById('submit_assignment');
  if (submitModal) {
    var observer = new MutationObserver(function(mutations) {
      mutations.forEach(function(mutation) {
        if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
          var display = window.getComputedStyle(submitModal).display;
          if (display !== 'none') {
            markAssignmentStarted(<%= @assignment.id %>);
          }
        }
      });
    });
    
    observer.observe(submitModal, { 
      attributes: true, 
      attributeFilter: ['style', 'class'] 
    });
  }
});
</script>

<%
# Copyright (C) 2011 - present Instructure, Inc.
#
# This file is part of Canvas.
#
# Canvas is free software: you can redistribute it and/or modify it under
# the terms of the GNU Affero General Public License as published by the Free
# Software Foundation, version 3 of the License.
#
# Canvas is distributed in the hope that it will be useful, but WITHOUT ANY
# WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS FOR
# A PARTICULAR PURPOSE. See the GNU Affero General Public License for more
# details.
#
# You should have received a copy of the GNU Affero General Public License along
# with this program. If not, see <http://www.gnu.org/licenses/>.
%>

<%css_bundle :group_submission_status %>

<% 
# Create a default submission object if none exists
@current_user_submission ||= OpenStruct.new(
  excused?: false,
  has_submission?: false,
  late?: false,
  submitted_at: nil,
  proxy_submitter: nil,
  submission_type: nil,
  url: nil,
  attachments: [],
  grade: nil,
  hide_grade_from_student?: false,
  rubric_assessment: nil,
  user: @current_user,
  visible_submission_comments: [],
  graded_anonymously: false,
  user_id: @current_user&.id
)
%>

<% cache([
  'submission_sidebar_render2',
  @current_user.id,
  @current_user_submission,
  Time.zone.utc_offset,
  @assigned_assessments&.count || 0,
  @assigned_assessments&.count(&:incomplete?) || 0,
  @assignment.anonymous_peer_reviews ? 1 : 0
].cache_key) do %>
<div class="details">
  <h2><%= t 'titles.submission', "Submission" %></h2>
  <% if @current_user_submission.excused? %>
    <div class="header">
      <%= t "Excused!" %>
     </div>
  <% elsif @assignment.expects_submission? %>
    <div class="header">
      <% if @current_user_submission.has_submission? %>
        <i class="icon-check" aria-hidden="true"></i>
        <%= t 'titles.submission_turned_in', "Submitted!" %>
      <% else %>
        <i class="icon-x" aria-hidden="true"></i>
        <%= t 'titles.submission_not_turned_in', "Not Submitted!" %>
      <% end %>
    </div>
  <% end %>
  <div class="content">
    <% if @current_user_submission.proxy_submitter %>
      <%= t "by %{name}", name: @current_user_submission.proxy_submitter.short_name %>
      <br />
    <% end %>
    
    <% if @current_user_submission.submitted_at %>
      <span class="<%= "late" if @current_user_submission.late? %>">
        <% if @current_user_submission.late? %>
          <%= t 'messages.submission_late_timestamp', "%{submitted_at} (late)", :submitted_at => datetime_string(@current_user_submission.submitted_at) %>
        <% else %>
          <%= datetime_string(@current_user_submission.submitted_at) %>
        <% end %>
      </span>
    <% else %>
      <span class="no-submission">
        <%= t 'messages.no_submission_yet', "No submission yet" %>
      </span>
    <% end %>

    <% if @current_user_submission.has_submission? || @current_user_submission.user_id %>
      <div>
        <a href="<%= context_url(@assignment.context, :context_assignment_submission_url, @assignment.id, @current_user_submission.user_id || @current_user.id)  %>">
          <%= t 'links.submission.details', "Submission Details" %>
        </a>
      </div>
    <% end %>

    <% if @current_user_submission.submission_type == "online_url" && @current_user_submission.url %>
      <div>
        <a href="<%= @current_user_submission.url %>" target="_new" id="view-original-link">
          <%= t 'links.submission.view_original_page', "View the Original Page" %>
        </a>
      </div>
    <% elsif @current_user_submission.submission_type == "online_upload" && @current_user_submission.attachments.any? %>
      <% @current_user_submission.attachments.each do |attachment| %>
        <div>
          <a href="<%= context_url(@context, :context_assignment_submission_url, @assignment.id, @current_user_submission.user_id, :download => attachment.id) %>">
            <%= t 'links.submission.download_attachment', "Download %{attachment_display_name}", {
              :attachment_display_name => attachment.display_name
            } %>
          </a>
        </div>
        <% js_bundle :progress_pill %>
        <span class="assignment_presenter_for_submission" style="display: none;"><%= AttachmentUploadStatus.upload_status(attachment) %></span>
        <span class="react_pill_container"></span>
      <% end %>
    <% elsif @current_user_submission.submission_type == "online_quiz" and @assignment&.quiz&.id %>
      <a href="<%= context_url(@assignment.context, :context_quiz_url, @assignment.quiz.id) %>" target="_new">
        <i class="icon-quiz" aria-hidden="true"></i>
        <%= @assignment.quiz.survey? ? t('links.submission.view_survey', "View the Survey Submission") :
          t('links.submission.view_quiz', "View the Quiz Submission") %>
      </a>
    <% end %>

    <div class="module">
      <% if @current_user_submission.grade && !@current_user_submission.hide_grade_from_student? %>
        <% if @current_user_submission.excused? %>
          <span><%= t "This assignment has been excused." %></span>
        <% else %>
         <% if !@assignment.restrict_quantitative_data?(@current_user, check_extra_permissions: true) %>
          <div><%= t 'labels.grade', "Grade: %{grade} (%{points_possible} pts possible)", {
            :grade => i18n_grade(@current_user_submission.grade, @assignment.grading_type),
            :points_possible => n(round_if_whole(@assignment.points_possible)),
            :wrapper => '<span style="font-size: 0.8em;">\1</span>'
          } %></div>
        <% else %>
          <div><%= t 'labels.rqd_grade', "Grade: %{grade}", {
            :grade => replace_dash_with_minus(@assignment.score_to_grade(@current_user_submission.score, @current_user_submission.grade, true)),
            :wrapper => '<span style="font-size: 0.8em;">\1</span>'
          } %></div>
        <% end %>
          <div><%= t 'labels.graded_anonymously', "Graded Anonymously: %{value}", {
            :value => @current_user_submission.graded_anonymously ? t('labels.yes', "yes") : t('labels.no', "no")
          } %></div>
        <% end %>
      <% else %>
        <% unless @current_user_submission.has_submission? %>
          <div class="no-grade-message">
            <%= t 'labels.not_graded_yet', "Not graded yet - no submission" %>
          </div>
        <% end %>
      <% end %>

      <% if @current_user_submission.rubric_assessment %>
        <a href="<%= context_url(@context, :context_assignment_submission_url, @assignment.id, @current_user_submission.user_id) %>#rubric" class='Submission__Link--has-icon'>
          <i class="icon-rubric" aria-hidden="true"></i>
          <%= t 'links.view_rubric_evaluation', "View Rubric Evaluation" %>
        </a>
      <% end %>
    </div>

    <% if @assignment.has_peer_reviews? && @current_user_submission.user == @current_user %>
      <h3><%= t 'titles.assigned_peer_reviews', "Assigned Peer Reviews" %></h3>

      <ul class="unstyled_list Submission__List">
        <li style="<%= hidden unless (@assigned_assessments&.empty? || @assigned_assessments.nil?) %>"><%= t 'labels.none_assigned', "None Assigned" %></li>
        <% (@assigned_assessments || []).each do |assessment| %>
          <li><%= student_peer_review_link_for @context, @assignment, assessment %></li>
        <% end %>
      </ul>
    <% end %>

    <div class="comments module">
      <% if @current_user_submission.hide_grade_from_student? %>
        <p><%= t 'You may not see all comments right now because the assignment is currently being graded.' %></p>
      <% else %>
        <h3><%= t 'titles.comments', "Comments:" %> </h3>

        <% visible_comments = @current_user_submission.visible_submission_comments || [] -%>
        <%= t('messages.no_comments', "No Comments") if visible_comments.empty? %>
        <% visible_comments.each do |comment| %>
          <div id="comment-<%= comment.id %>" class="comment">
            <div class="comment_content" data-content="<%= comment.comment %>"></div>
            <div class="comment_attachments">
              <% (comment.attachments || []).each do |attachment| %>
                <div class="comment_attachment">
                  <a href="<%= context_url(@context, :context_assignment_submission_url, @assignment.id, @current_user_submission.user_id, :comment_id => comment.id, :download => attachment.id) %>" class="comment_attachment_link <%= attachment.mime_class %>"><%= attachment.display_name %></a>
                </div>
              <% end %>
            </div>
            <div class="signature" style="font-size: 0.8em; text-align: <%= direction('right') %>;">
              <%= t :comment_signature, "%{author}, %{created_at}", {
                :author => comment_author_name_for(comment),
                :created_at => datetime_string(comment.created_at)
              } %>
            </div>
          </div>
        <% end %>
      <% end %>
    </div>
  </div>
</div>
<% end %>
<%
# Copyright (C) 2016 - present Instructure, Inc.
#
# This file is part of Canvas.
#
# Canvas is free software: you can redistribute it and/or modify it under
# the terms of the GNU Affero General Public License as published by the Free
# Software Foundation, version 3 of the License.
#
# Canvas is distributed in the hope that it will be useful, but WITHOUT ANY
# WARRANTY; without even the implied warranty of MERCHANT<PERSON>ILITY or FITNESS FOR
# A PARTICULAR PURPOSE. See the GNU Affero General Public License for more
# details.
#
# You should have received a copy of the GNU Affero General Public License along
# with this program. If not, see <http://www.gnu.org/licenses/>.
%>

<%= form_for :account_notification,
  url: {controller: 'account_notifications', action: 'update', id: announcement.id},
  html: {
    id: "edit_notification_form_#{announcement.id}",
    class: 'edit_notification_form hidden_form',
    role: 'region',
    method: 'put' } do |f| %>

  <div class="grid-row">

    <div class="col-xs-12 col-lg-6">
      <div class="ic-Form-control">
        <label for="<%= "account_notification_subject_#{announcement.id}" %>" class="ic-Label">
          <%= t("Title") %>
        </label>
        <%= f.text_field :subject, :value => announcement.subject, :class => 'ic-Input', :id => "account_notification_subject_#{announcement.id}" %>
      </div>
    </div>

    <div class="col-xs-12 col-lg-6">
      <div class="ic-Form-control">
        <label for="<%= "account_notification_icon_#{announcement.id}" %>" class="ic-Label">
          <%= t("Announcement type") %>
        </label>
        <select id="<%= "account_notification_icon_#{announcement.id}" %>" class="ic-Input" name="account_notification[icon]">
          <%= options_for_select([[t("information"), "information", {:class=>"information"}],
            [t("error"), "error", {:class=>"error"}],
            [t("warning"), "warning", {:class=>"warning"}],
            [t("question"), "question", {:class=>"question"}],
            [t("calendar"), "calendar", {:class=>"calendar"}]], announcement.icon) %>
        </select>
      </div>
    </div>

  </div>

  <div class="ic-Form-control">
    <label for="account_notification_message_<%= announcement.id %>" class="ic-Label">
      <%= t("Message") %>
    </label>
    <%= f.text_area :message, :value => announcement.message, :id => "account_notification_message_#{announcement.id}", :class => 'alert_message edit_notification_form' %>
    <% if @account.site_admin? %>
      <p><%= t("Enter \"{{ACCOUNT_DOMAIN}}\" to substitute the user's root account domain") %></p>
      <p><%= t("Enter \"{{CANVAS_USER_ID}}\" to substitute the user's unique Canvas ID") %></p>
    <% end %>
  </div>

  <% if @account.root_account.trust_exists? %>
    <div class="ic-Form-control">
      <%= f.check_box :domain_specific, :checked => announcement.domain_specific, :id => "domain_specific_#{announcement.id}" %>
      <%= f.label :domain_specific, t("Only show announcement on current account domain") %>
    </div>
  <% end %>

  <div class="ic-Form-control">
    <div class="ic-Label" id="aria-announcements-send-to-group">
      <%= t "Show to" %>
      <%= roles_message(@account) %>
    </div>
    <div class="grid-row">
      <div class="col-xs-12 col-lg-4">
        <i><%= t "Course roles" %></i>
        <div class="ic-Checkbox-group" role="group" aria-labelledby="aria-announcements-send-to-group">
          <% role_ids = announcement.account_notification_roles.map {|r| r.role_id} %>
          <% @course_roles.each do |r| %>
            <% course_role_checked = role_ids.include? r[:id] %>
            <div class="ic-Form-control ic-Form-control--checkbox">
              <%= check_box_tag "account_notification_roles[]", r[:id], course_role_checked, { :class => "account_notification_role_cbx", :id => "account_notification_role_#{r[:id]}_cbx_#{announcement.id}" } %>
              <label class="ic-Label" for="<%= "account_notification_role_#{r[:id]}_cbx_#{announcement.id}" %>">
                <%= r[:label] %>
                <span class="screenreader-only">
                  <%=t "Send this announcement to users with the course role of %{role}", :role => r[:label] %>
                </span>
              </label>
            </div>
          <% end %>
          <% if @account.root_account? %>
            <% nil_role_checked = role_ids.include? nil %>
            <div class="ic-Form-control ic-Form-control--checkbox">
              <%= check_box_tag "account_notification_roles[]", "NilEnrollment", nil_role_checked, { :class => "account_notification_role_cbx", :id => "account_notification_role_NilEnrollment_cbx_#{announcement.id}" } %>
              <label class="ic-Label" for="<%= "account_notification_role_NilEnrollment_cbx_#{announcement.id}" %>">
                <%= t "Unenrolled users" %>
                <span class="screenreader-only">
                  <%=t "Send this announcement to unenrolled users" %>
                </span>
              </label>
            </div>
          <% end %>
        </div>
      </div>
      <div class="col-xs-12 col-lg-4">
        <i><%= t "Account roles" %></i>
        <% @account_roles.each do |r| %>
          <% account_role_checked = role_ids.include? r[:id] %>
          <div class="ic-Form-control ic-Form-control--checkbox">
            <%= check_box_tag "account_notification_roles[]", r[:id], account_role_checked, { :class => "account_notification_role_cbx", :id => "account_notification_role_#{r[:id]}_cbx_#{announcement.id}" } %>
            <label class="ic-Label" for="<%= "account_notification_role_#{r[:id]}_cbx_#{announcement.id}" %>">
              <%= r[:label] %>
              <span class="screenreader-only">
                <%=t "Send this announcement to users with the account role of %{role}", :role => r[:label] %>
              </span>
            </label>
          </div>
        <% end %>
      </div>
    </div>
  </div>

  <div class="grid-row teacher-options" style="display: none;">
    <div class="col-xs-12 col-lg-6">
      <div class="ic-Form-control">
        <label for="<%= "account_notification_category_#{announcement.id}" %>" class="ic-Label">
          <%= t("Category") %>
        </label>
        <select id="<%= "account_notification_category_#{announcement.id}" %>" class="ic-Input" name="account_notification[category]">
          <%= options_for_select([[t("Urgent"), "Urgent"],
            [t("Event"), "Event"],
            [t("Reminder"), "Reminder"],
            [t("General"), "General"]], announcement.category) %>
        </select>
      </div>
    </div>
    <div class="col-xs-12 col-lg-6">
      <div class="ic-Form-control">
        <label for="<%= "account_notification_priority_#{announcement.id}" %>" class="ic-Label">
          <%= t("Priority") %>
        </label>
        <select id="<%= "account_notification_priority_#{announcement.id}" %>" class="ic-Input" name="account_notification[priority]">
          <%= options_for_select([["1", 1], ["2", 2], ["3", 3], ["4", 4], ["5", 5]], announcement.priority) %>
        </select>
      </div>
    </div>
  </div>

  <div class="grid-row">
    <div class="col-xs-12 col-lg-6">
      <div class="ic-Form-control">
        <label class="ic-Label" id="announcement_starts_at_label">
          <%= t("Announcement starts at") %>
          <span class="screenreader-only">
            <%= datepicker_screenreader_prompt %>
          </span>
        </label>
        <% if announcement.start_at < Date.today %>
          <%= f.text_field :start_at,
                           :value => datetime_string(announcement.start_at),
                           :id => "account_notification_start_at_#{announcement.id}",
                           "aria-labelledby" => "announcement_starts_at_label",
                           "data-initial-value" => announcement.start_at.iso8601,
                           :title => accessible_date_format,
                           :readonly => true %>
        <% else %>
          <%= f.text_field :start_at,
                           :value => datetime_string(announcement.start_at),
                           :id => "account_notification_start_at_#{announcement.id}",
                           :class => 'datetime_field',
                           "aria-labelledby" => "announcement_starts_at_label",
                           "data-initial-value" => announcement.start_at.iso8601,
                           "data-tooltip" => "",
                           :title => accessible_date_format %>
        <% end %>
      </div>
    </div>
    <div class="col-xs-12 col-lg-6">
      <div class="ic-Form-control">
        <label class="ic-Label" id="announcement_ends_at_label">
          <%= t("Announcement ends at") %>
          <span class="screenreader-only">
            <%= datepicker_screenreader_prompt %>
          </span>
        </label>
        <% if announcement.end_at < Date.today %>
          <%= f.text_field :end_at,
                           :value => datetime_string(announcement.end_at),
                           :id => "account_notification_end_at_#{announcement.id}",
                           "aria-labelledby" => "announcement_ends_at_label",
                           "data-initial-value" => announcement.end_at.iso8601,
                           :title => accessible_date_format,
                           :readonly => true %>
        <% else %>
          <%= f.text_field :end_at,
                           :value => datetime_string(announcement.end_at),
                           :id => "account_notification_end_at_#{announcement.id}",
                           :class => 'datetime_field',
                           "aria-labelledby" => "announcement_ends_at_label",
                           "data-initial-value" => announcement.end_at.iso8601,
                           "data-tooltip" => "",
                           :title => accessible_date_format %>
        <% end %>
      </div>
    </div>
  </div>

  <div class="ic-Form-control ic-Form-control--checkbox">
    <%= f.check_box :is_public, :checked => announcement.is_public,
        :id => "account_notification_is_public_#{announcement.id}" %>
    <%= f.label :is_public, t("Make this announcement public (visible on login page for unauthenticated users)"),
        :class => "ic-Label", :for => "account_notification_is_public_#{announcement.id}" %>
  </div>

  <% if !@account.site_admin? %>
    <div class="ic-Form-control ic-Form-control--checkbox">
      <%= f.check_box :send_message, :checked => announcement.send_message? && !announcement.messages_sent_at,
          :id => "account_notification_send_message_#{announcement.id}" %>
      <% if announcement.messages_sent_at? %>
        <%= f.label :send_message, t("Re-send notification directly to users when announcement starts"),
            :class => "ic-Label", :for => "account_notification_send_message_#{announcement.id}" %>
      <% else %>
        <%= f.label :send_message, t("Send notification directly to users when announcement starts"),
            :class => "ic-Label", :for => "account_notification_send_message_#{announcement.id}" %>
      <% end %>
    </div>
  <% end %>

  <div class="ic-Form-actions">
    <%= button_tag(t("Cancel"), :class => "element_toggler btn button-secondary edit_cancel_focus", 'aria-controls' => "edit_notification_form_#{announcement.id}", 'data-cancel-focus-id' => announcement.id) %>
    <%= button_tag(t("Save Changes"), :class => 'btn btn-primary') %>
  </div>
  
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Directly target the Teacher checkbox and teacher-options div
      var teacherCheckbox = document.getElementById('account_notification_role_12_cbx');
      var teacherOptions = document.querySelector('.teacher-options');
      
      if (teacherCheckbox && teacherOptions) {
        // Set initial state
        teacherOptions.style.display = teacherCheckbox.checked ? 'flex' : 'none';
        
        // Add change listener
        teacherCheckbox.addEventListener('change', function() {
          teacherOptions.style.display = this.checked ? 'flex' : 'none';
        });
      } else {
        console.error('Could not find teacher checkbox or options', {
          'teacherCheckbox': teacherCheckbox,
          'teacherOptions': teacherOptions
        });
      }
    });
  </script>
<% end %>

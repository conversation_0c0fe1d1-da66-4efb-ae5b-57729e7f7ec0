.details {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  border: 1px solid #e9ecef;
  margin-bottom: 20px;
}

.details h2 {
  background: linear-gradient(135deg, #2d5a3d 0%, #4a7c59 100%);
  color: white;
  padding: 16px;
  margin: 0;
  font-size: 1.4rem;
  font-weight: 600;
  position: relative;
  overflow: hidden;
}

.details h2::before {
  content: "";
  position: absolute;
  top: -50%;
  right: -50%;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  transform: rotate(45deg);
}

.details .header {
  background: #f8f9fa;
  padding: 12px 16px;
  margin: 0;
  border-bottom: 1px solid #f0f2f5;
  display: flex;
  align-items: center;
  gap: 10px;
  font-weight: 500;
  color: #495057;
}

.details .header .icon-check {
  background: #d4edda;
  color: #155724;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.details .header .icon-x {
  background: #f8d7da;
  color: #721c24;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.details .content {
  padding: 0;
}

.details .content > div:not(.module):not(.comments) {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f2f5;
}

.details .content > div:last-child {
  border-bottom: none;
}

.details .content > span {
  display: block;
  padding: 12px 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #f0f2f5;
  font-size: 0.9rem;
  color: #495057;
}

.details .content > span.late {
  background: #fff3cd;
  color: #856404;
  font-weight: 500;
}

.details .content > span.no-submission {
  background: #f8d7da;
  color: #721c24;
  font-weight: 500;
  font-style: italic;
}

.details a {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 10px 14px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  text-decoration: none;
  color: #495057;
  transition: all 0.2s ease;
  font-size: 0.9rem;
  margin-bottom: 8px;
  width: 100%;
  box-sizing: border-box;
}

.details a:hover {
  background: #e9f7ef;
  border-color: #4a7c59;
  color: #2d5a3d;
  transform: translateY(-1px);
  text-decoration: none;
}

.details a::before {
  content: "📄";
  margin-right: 4px;
}

.details a[href*="download"]::before {
  content: "⬇️";
}

.details a[href*="quiz"]::before {
  content: "📝";
}

.details a[href*="rubric"]::before,
.details a.Submission__Link--has-icon::before {
  content: "📊";
}

.details .module {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 8px;
  padding: 16px;
  margin: 16px;
  border: 1px solid #e9ecef;
}

.details .module div:first-of-type {
  text-align: center;
  margin-bottom: 8px;
}

.details .module div:last-of-type {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.85rem;
  color: #6c757d;
  padding-top: 4px;
  border-top: 1px solid #dee2e6;
}

.details .module .no-grade-message {
  text-align: center;
  color: #6c757d;
  font-style: italic;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 4px;
  border: 1px dashed #dee2e6;
}

.details h3 {
  color: #2d5a3d;
  font-size: 1.1rem;
  margin: 0 0 12px 0;
  padding: 16px 16px 0 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.details h3::before {
  content: "👥";
  font-size: 1rem;
}

.details .Submission__List {
  list-style: none;
  padding: 0 16px;
  margin: 0;
}

.details .Submission__List li {
  padding: 10px 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 3px solid #4a7c59;
  margin-bottom: 8px;
  font-size: 0.9rem;
}

.details .Submission__List li[style*="display: none"],
.details .Submission__List li:empty::after {
  content: "📋 None Assigned";
  color: #6c757d;
  font-style: italic;
  text-align: center;
  display: block;
  padding: 16px;
  background: transparent;
  border: none;
}

.details .comments {
  padding: 16px;
  border-top: 1px solid #f0f2f5;
}

.details .comments h3 {
  padding: 0;
  margin-bottom: 12px;
}

.details .comments h3::before {
  content: "💬";
}

.details .comment {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 14px;
  margin-bottom: 10px;
  border-left: 3px solid #4a7c59;
}

.details .comment_content {
  color: #495057;
  margin-bottom: 10px;
  line-height: 1.5;
}

.details .comment_attachments {
  margin-bottom: 10px;
}

.details .comment_attachment {
  display: inline-block;
  margin-right: 10px;
  margin-bottom: 4px;
}

.details .comment_attachment_link {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: #e9ecef;
  border-radius: 16px;
  text-decoration: none;
  color: #495057;
  font-size: 0.8rem;
  transition: background-color 0.2s ease;
  width: auto;
  margin-bottom: 0;
}

.details .comment_attachment_link:hover {
  background: #dee2e6;
  transform: none;
}

.details .comment_attachment_link::before {
  content: "📎";
  margin-right: 0;
}

.details .signature {
  text-align: right;
  font-size: 0.75rem;
  color: #6c757d;
  font-style: italic;
  border-top: 1px solid #e9ecef;
  padding-top: 6px;
  margin-top: 6px;
}

.assignment_presenter_for_submission,
.react_pill_container {
  margin-top: 6px;
}

@media (max-width: 480px) {
  .details {
    border-radius: 8px;
  }

  .details h2 {
    padding: 14px;
    font-size: 1.2rem;
  }

  .details .content > div {
    padding: 12px 14px;
  }

  .details .module {
    margin: 12px;
    padding: 14px;
  }

  .details a {
    padding: 8px 10px;
    font-size: 0.85rem;
  }
}

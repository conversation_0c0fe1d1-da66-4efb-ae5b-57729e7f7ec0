/*
 * Copyright (C) 2015 - present Instructure, Inc.
 *
 * This file is part of Canvas.
 *
 * Canvas is free software: you can redistribute it and/or modify it under
 * the terms of the GNU Affero General Public License as published by the Free
 * Software Foundation, version 3 of the License.
 *
 * Canvas is distributed in the hope that it will be useful, but WITHOUT ANY
 * WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS FOR
 * A PARTICULAR PURPOSE. See the GNU Affero General Public License for more
 * details.
 *
 * You should have received a copy of the GNU Affero General Public License along
 * with this program. If not, see <http://www.gnu.org/licenses/>.
 */

@import "base/environment";

/* Brand Colors */
:root {
  --primary-green: #2d5b3f;
  --secondary-orange: #f4a534;
  --light-green: #b8d4c2;
  --dark-green: #1a3d2b;
  --light-gray: #f8f9fa;
  --border-color: #e9ecef;
  --text-gray: #6c757d;
  --white: #ffffff;
}

.ic-Layout-contentMain {
  padding: 0;
}

/* Base Report Styles */
.report_description {
  display: none;
}

table.report_example, table.report {
  border-collapse: collapse;
  page-break-inside: avoid;
  thead, th {
    background-color: #ccc;
  }
  td, th {
    border: 1px solid #aaa;
    padding: 2px 5px;
  }
}

#tab-reports {
  .report_dialog {
    padding: 15px;
  }
  table.reports {
    margin: 5px 15px;
    width: 90%;
    border-collapse: collapse;
    tr.reports {
      border-bottom: 1px solid #DDD;
      th.reports {
        padding: 5px;
        background-color: #DDD;
        font-weight: bold;
        &.title {
          text-align: left;
          background-color: $ic-color-light;
        }
      }
      td.reports {
        padding: 5px;
        &.title {
          font-weight: bold;
        }
        &.action {
          text-align: center;
        }
      }
    }
  }
}

table.report.sortable {
  th.tablesorter-header {
    padding-#{direction(right)}: 20px;
    cursor: pointer;
    background: url(/images/tablesorter/bg.png) no-repeat right center;
    &:hover {
      background-image: url(/images/tablesorter/bg_hover.png);
    }
    &.tablesorter-headerAsc {
      background-image: url(/images/tablesorter/asc.gif);
    }
    &.tablesorter-headerDesc {
      background-image: url(/images/tablesorter/desc.gif);
    }
    &.sorter-false {
      background-image: none;
      cursor: default;
    }
  }
}

.report.sortable {
  background: var(--white);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(44,62,80,0.07);
  margin-bottom: 2rem;
  overflow: hidden;
}

.report.sortable th, .report.sortable td {
  padding: 0.75em 1em;
  border: none;
}

.report.sortable thead th {
  background: var(--light-gray);
  color: #2c3e50;
  font-weight: 600;
  border-bottom: 2px solid #e1e4ea;
}

.report.sortable tbody tr {
  transition: background 0.2s;
}

.report.sortable tbody tr:hover {
  background: #f8fafc;
}

/* =================================================================
   TEACHER ACTIVITY REPORT - MAIN CONTAINER
   ================================================================= */

.teacher-activity-container {
  min-height: 100vh;
  padding: 1.5rem 1rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

/* =================================================================
   HEADER SECTION
   ================================================================= */

.modern-header {
  background: var(--white);
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid var(--border-color);
}

.modern-header h1 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--primary-green);
  margin: 0 0 0.5rem 0;
}

.modern-header .subtitle {
  font-size: 0.95rem;
  color: var(--text-gray);
  margin: 0;
}

/* =================================================================
   COURSE SECTION
   ================================================================= */

.course-section {
  background: var(--white);
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid var(--border-color);
}

.course-header {
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 3px solid var(--secondary-orange);
}

.course-title {
  font-size: 1.4rem;
  font-weight: 600;
  color: var(--primary-green);
  margin: 0;
}

/* =================================================================
   ALERT SECTION
   ================================================================= */

.alert-section {
  background: linear-gradient(135deg, var(--secondary-orange) 0%, #e69500 100%);
  color: white;
  padding: 1.5rem;
  border-radius: 12px;
  margin-bottom: 2rem;
  box-shadow: 0 4px 12px rgba(244, 165, 52, 0.3);
}

.alert-section h3 {
  margin: 0 0 1rem 0;
  font-size: 1.2rem;
  font-weight: 600;
}

.alert-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
}

.alert-item {
  background: rgba(255, 255, 255, 0.2);
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-size: 0.9rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

/* =================================================================
   TAB NAVIGATION - CLEAN MODERN STYLE
   ================================================================= */

.main-tab-navigation, .chart-navigation {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-bottom: 2rem;
  background: transparent;
  padding: 0;
  border: none;
  border-bottom: 1px solid #e0e0e0;
}

.main-tab-btn, .chart-tab-btn {
  background: transparent;
  border: none;
  color: #666;
  padding: 1rem 0;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  font-size: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  position: relative;
  border-bottom: 3px solid transparent;
}

.main-tab-btn.active, .chart-tab-btn.active {
  color: var(--primary-green);
  border-bottom-color: var(--secondary-orange);
  background: transparent;
  transform: none;
  box-shadow: none;
}

.main-tab-btn:hover:not(.active), .chart-tab-btn:hover:not(.active) {
  color: var(--primary-green);
  background: transparent;
  transform: none;
  box-shadow: none;
}

/* Tab Content */
.main-tab-content, .chart-tab-content {
  display: none;
  animation: fadeIn 0.4s ease-in;
}

.main-tab-content.active, .chart-tab-content.active {
  display: block;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(15px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* =================================================================
   STUDENTS GRID
   ================================================================= */

.students-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

/* =================================================================
   STUDENT CARD - IMPROVED DESIGN
   ================================================================= */

.student-card {
  background: var(--white);
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
  position: relative;
}

.student-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.12);
}

/* Student Header */
.student-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1.5rem;
  padding-bottom: 0.75rem;
  border-bottom: 2px solid var(--light-gray);
}

.student-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--primary-green);
  text-decoration: none;
  transition: color 0.2s ease;
}

.student-name:hover {
  color: var(--secondary-orange);
}

.student-actions {
  display: flex;
  gap: 0.75rem;
}

/* =================================================================
   STUDENT METRICS - ENHANCED
   ================================================================= */

.student-metrics {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1.5rem;
  border-radius: 12px;
}

.metric {
  text-align: center;
  padding: 1rem;
  background: var(--white);
  border-radius: 12px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
  border: 1px solid var(--border-color);
  transition: transform 0.2s ease;
}

.metric:hover {
  transform: translateY(-2px);
}

.metric-value {
  font-size: 1.4rem;
  font-weight: 700;
  color: var(--primary-green);
  margin-bottom: 0.5rem;
  display: block;
}


.metric:hover {
  transform: translateY(-2px);
}

.metric-value {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--primary-green);
  margin-bottom: 0.5rem;
  display: block;
}

.metric-label {
  font-size: 0.9rem;
  color: var(--text-gray);
  font-weight: 500;
  margin-bottom: 1rem;
}

.progress-bar {
  height: 8px;
  background: #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-green) 0%, var(--secondary-orange) 100%);
  border-radius: 4px;
  transition: width 0.8s ease;
}

/* =================================================================
   SUBMISSION SUMMARY - ENHANCED
   ================================================================= */

.submission-summary {
  margin-bottom: 1.5rem;
  background: var(--white);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 1rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.submission-breakdown {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.breakdown-section {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.breakdown-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  font-size: 0.9rem;
  border-bottom: 1px solid var(--light-gray);
}

.breakdown-item:last-child {
  border-bottom: none;
}

.breakdown-label {
  font-weight: 600;
  color: #495057;
  min-width: 140px;
}

.breakdown-value {
  color: #212529;
  text-align: right;
  flex-grow: 1;
  font-weight: 500;
}

.breakdown-item.manual-grades {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  border-radius: 8px;
  padding: 0.75rem 1rem;
  margin: 0.5rem 0;
  border: none;
}

.breakdown-item.totals {
  border-top: 2px solid var(--secondary-orange);
  padding-top: 0.75rem;
  margin-top: 0.75rem;
  font-weight: 600;
  background: var(--light-gray);
  border-radius: 8px;
  padding: 0.75rem 1rem;
  border-bottom: none;
}

.manual-note {
  font-size: 0.8rem;
  color: var(--text-gray);
  font-style: italic;
  margin-left: 0.5rem;
}

/* =================================================================
   ACCESS SUMMARY
   ================================================================= */

.access-summary {
  background: var(--light-gray);
  padding: 1rem;
  border-radius: 12px;
  border-left: 4px solid var(--primary-green);
  margin-bottom: 1.5rem;
  border: 1px solid var(--border-color);
}

.access-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  margin-bottom: 1rem;
}

.stat {
  text-align: center;
  padding: 1rem;
  background: var(--white);
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.stat .label {
  display: block;
  font-size: 0.8rem;
  margin-bottom: 0.5rem;
}

.stat .value {
  display: block;
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--primary-green);
}

.last-interaction {
  font-size: 0.9rem;
  color: var(--text-gray);
  text-align: center;
  margin-bottom: 1rem;
  line-height: 1.4;
}

.access-link {
  display: block;
  text-align: center;
  padding: 0.75rem 1rem;
  background: var(--primary-green);
  color: white;
  text-decoration: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.access-link:hover {
  background: var(--dark-green);
  color: white;
  transform: translateY(-1px);
}

/* =================================================================
   UNGRADED ASSIGNMENTS
   ================================================================= */

.ungraded-assignments {
  padding: 1rem;
  background: #fff8e1;
  border: 1px solid #ffcc02;
  border-radius: 12px;
  border-left: 4px solid var(--secondary-orange);
}

.ungraded-title {
  font-weight: 600;
  color: #b8860b;
  margin-bottom: 1rem;
  font-size: 1rem;
}

.assignment-link {
  display: block;
  padding: 0.5rem;
  background: var(--white);
  border: 1px solid #ffcc02;
  border-radius: 8px;
  color: var(--primary-green);
  text-decoration: none;
  transition: all 0.2s ease;
  font-weight: 500;
}

.assignment-link:hover {
  background: var(--light-gray);
  transform: translateX(4px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.assignment-date {
  font-size: 0.8rem;
  color: var(--text-gray);
  margin-bottom: 0.5rem;
  margin-left: 1rem;
}

/* =================================================================
   STUDENT FLAGS - ENHANCED
   ================================================================= */

.student-flags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  margin-top: 1.5rem;
}

.flag {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
  border: 1px solid transparent;
  transition: all 0.2s ease;
}

.flag:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.flag.info {
  background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
  color: #0c5460;
  border-color: #bee5eb;
}

.flag.good {
  background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
  color: #155724;
  border-color: #c3e6cb;
}

.flag.warning {
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
  color: #856404;
  border-color: #ffeaa7;
}

.flag.critical {
  background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
  color: #721c24;
  border-color: #f5c6cb;
}

/* =================================================================
   CHARTS SECTION
   ================================================================= */


.charts-header {
  text-align: center;
  margin-bottom: 2.5rem;
}

.charts-header h3 {
  font-size: 1.75rem;
  font-weight: 600;
  color: var(--primary-green);
  margin: 0 0 1rem 0;
}

.charts-header p {
  color: var(--text-gray);
  font-size: 1.1rem;
  margin: 0;
}

.metric-description {
  background: var(--white);
  padding: 1.5rem;
  border-radius: 12px;
  margin-bottom: 1.5rem;
  border-left: 4px solid var(--primary-green);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid var(--border-color);
}

.metric-description h4 {
  margin: 0 0 0.75rem 0;
  color: var(--primary-green);
  font-size: 1.2rem;
  font-weight: 600;
}

.metric-description p {
  margin: 0;
  color: var(--text-gray);
  font-size: 1rem;
  line-height: 1.6;
}

.chart-container {
  position: relative;
  background: var(--white);
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  margin-bottom: 1.5rem;
  border: 1px solid var(--border-color);
  height: 350px;
  overflow: hidden;
}

.chart-container.engagement-chart {
  height: 450px; /* Slightly taller for dual-axis chart */
}
.course-activity-chart {
  background: transparent;
  border-radius: 8px;
  margin: 0 auto;
  display: block;
  max-width: 100%;
}

.metric-stats {
  display: flex;
  justify-content: space-around;
  margin-top: 1.5rem;
  padding: 1.5rem;
  background: var(--white);
  border-radius: 12px;
  font-size: 0.9rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid var(--border-color);
}

.metric-stat {
  text-align: center;
  flex: 1;
}

.metric-stat .stat-value {
  display: block;
  font-weight: 600;
  color: var(--primary-green);
  font-size: 1.4rem;
  margin-bottom: 0.5rem;
}

.metric-stat .stat-label {
  color: var(--text-gray);
  font-size: 0.9rem;
  font-weight: 500;
}

/* =================================================================
   GRADE DISTRIBUTION ANALYSIS
   ================================================================= */

.grade-distribution-container {
  margin-bottom: 2rem;
}

.assignments-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.assignment-card {
  background: var(--white);
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid var(--border-color);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.assignment-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
}

.assignment-card.no-grades {
  border-left: 4px solid #6c757d;
  background: linear-gradient(135deg, var(--light-gray) 0%, var(--white) 100%);
}

.assignment-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid var(--secondary-orange);
}

.assignment-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--primary-green);
  margin: 0;
  line-height: 1.3;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.assignment-type {
  font-size: 0.8rem;
  background: var(--secondary-orange);
  color: white;
  padding: 0.2rem 0.6rem;
  border-radius: 12px;
  font-weight: 500;
  align-self: flex-start;
}

.assignment-type.quiz {
  background: #3498db;
}

.assignment-status {
  font-size: 0.75rem;
  padding: 0.15rem 0.5rem;
  border-radius: 10px;
  font-weight: 500;
  align-self: flex-start;
}

.assignment-status.not-graded {
  background: #6c757d;
  color: white;
}

.assignment-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.25rem;
}

.assignment-points {
  font-size: 0.9rem;
  color: var(--text-gray);
  font-weight: 500;
  white-space: nowrap;
}

.assignment-due {
  font-size: 0.8rem;
  color: #888;
  font-style: italic;
}

.grade-chart-container {
  position: relative;
  background: var(--white);
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
  height: 200px;
  border: 1px solid var(--border-color);
}

.grade-distribution-chart {
  background: transparent;
}

.grade-breakdown {
  background: var(--light-gray);
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid var(--border-color);
  margin-bottom: 1rem;
}

.breakdown-title {
  font-weight: 600;
  color: var(--primary-green);
  margin-bottom: 0.75rem;
  font-size: 0.9rem;
}

.breakdown-bars {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.breakdown-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 0.8rem;
}

.breakdown-label {
  min-width: 80px;
  color: var(--text-gray);
  font-weight: 500;
}

.breakdown-bar {
  flex: 1;
  height: 16px;
  background: #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
}

.breakdown-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-green) 0%, var(--secondary-orange) 100%);
  transition: width 0.3s ease;
}

.breakdown-count {
  min-width: 24px;
  text-align: center;
  font-weight: 600;
  color: var(--primary-green);
}

.grade-stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
  margin-bottom: 1.5rem;
  margin-top: 1rem;
}

.grade-stat {
  text-align: center;
  padding: 0.75rem;
  background: var(--light-gray);
  border-radius: 8px;
  border: 1px solid var(--border-color);
}

.grade-stat .stat-value {
  display: block;
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--primary-green);
  margin-bottom: 0.25rem;
}

.grade-stat .stat-label {
  display: block;
  font-size: 0.8rem;
  color: var(--text-gray);
  margin-bottom: 0.25rem;
}

.grade-stat .stat-percentage {
  font-size: 0.75rem;
  color: var(--secondary-orange);
  font-weight: 500;
}

.missing-submissions {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 6px;
  padding: 0.5rem;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.missing-icon {
  font-size: 1rem;
}

.missing-text {
  font-size: 0.85rem;
  color: #856404;
  font-weight: 500;
}

.assignment-alert {
  background: #dc3545;
  color: white;
  padding: 0.75rem;
  border-radius: 6px;
  font-size: 0.85rem;
  margin-top: 1rem;
  font-weight: 500;
}

.assignment-alert.warning {
  background: var(--secondary-orange);
}

.assignment-status-info {
  padding: 1rem;
  background: var(--light-gray);
  border-radius: 8px;
  border: 1px solid var(--border-color);
}

.status-message {
  padding: 0.75rem;
  margin-bottom: 0.75rem;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 500;
}

.basic-stats {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-top: 1rem;
}

.basic-stat {
  text-align: center;
  padding: 0.75rem;
  background: var(--white);
  border-radius: 6px;
  border: 1px solid var(--border-color);
}

.basic-stat .stat-value {
  display: block;
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--primary-green);
  margin-bottom: 0.25rem;
}

.basic-stat .stat-label {
  font-size: 0.8rem;
  color: var(--text-gray);
}

.no-submissions {
  text-align: center;
  padding: 2rem 1rem;
  color: var(--text-gray);
  background: var(--light-gray);
  border-radius: 8px;
  border: 1px solid var(--border-color);
}

.course-performance-summary {
  background: var(--white);
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid var(--border-color);
  margin-bottom: 2rem;
}

.course-performance-summary h4 {
  color: var(--primary-green);
  margin: 0 0 1rem 0;
  font-size: 1.2rem;
  font-weight: 600;
}

.summary-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.summary-card {
  text-align: center;
  padding: 1rem;
  background: var(--light-gray);
  border-radius: 8px;
  border: 1px solid var(--border-color);
  transition: transform 0.2s ease;
}

.summary-card:hover {
  transform: translateY(-2px);
}

.summary-card .summary-value {
  display: block;
  font-size: 1.4rem;
  font-weight: 600;
  color: var(--primary-green);
  margin-bottom: 0.5rem;
}

.summary-card .summary-label {
  display: block;
  font-size: 0.9rem;
  color: var(--text-gray);
  font-weight: 500;
}

.summary-detail {
  display: block;
  font-size: 0.75rem;
  color: #888;
  margin-top: 0.25rem;
  font-weight: normal;
}

.summary-alerts {
  margin-top: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.summary-alert {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  color: #856404;
  padding: 0.75rem;
  border-radius: 6px;
  font-size: 0.9rem;
}

.no-grades-message {
  text-align: center;
  padding: 3rem 1rem;
  color: var(--text-gray);
}

.no-grades-message h4 {
  color: var(--primary-green);
  margin-bottom: 1rem;
}

.create-assignment-prompt {
  background: #e8f5e8;
  border: 1px solid #c3e6c3;
  color: var(--primary-green);
  padding: 1rem;
  border-radius: 8px;
  margin-top: 1rem;
}

.create-assignment-prompt p {
  margin: 0;
  font-size: 0.9rem;
}

/* Student Grades Toggle Styles */
.student-grades-section {
  margin-top: 1.5rem;
  border-top: 1px solid var(--border-color);
  padding-top: 1rem;
}

.student-grades-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.student-grades-header h5 {
  margin: 0;
  color: var(--primary-green);
  font-size: 1rem;
}

.toggle-grades-btn {
  background: #3498db;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.85rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: background-color 0.3s;
}

.toggle-grades-btn:hover {
  background: #2980b9;
}

.grades-table {
  background: var(--light-gray);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.grades-header {
  background: #34495e;
  color: white;
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1.2fr 1.5fr;
  gap: 1px;
  padding: 0.75rem;
  font-weight: bold;
  font-size: 0.85rem;
}

.grade-row {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1.2fr 1.5fr;
  gap: 1px;
  padding: 0.75rem;
  border-bottom: 1px solid var(--border-color);
  background: var(--white);
  font-size: 0.85rem;
}

.grade-row:last-child {
  border-bottom: none;
}

.grade-row.missing {
  background: #fef2f2;
}

.grade-row.pending {
  background: #fefbf2;
}

.grade-row.graded {
  background: #f0f9ff;
}

.student-name {
  font-weight: 500;
  color: #2c3e50;
}

.no-score, .no-percentage, .no-submission {
  color: #95a5a6;
  font-style: italic;
}

.percentage {
  font-weight: 500;
}

.status-badge {
  padding: 0.2rem 0.5rem;
  border-radius: 10px;
  font-size: 0.75rem;
  font-weight: 500;
  display: inline-block;
}

.status-badge.graded {
  background: #d4edda;
  color: #155724;
}

.status-badge.pending {
  background: #fff3cd;
  color: #856404;
}

.status-badge.missing {
  background: #f8d7da;
  color: #721c24;
}

.status-badge.in_progress {
  background: #cce5ff;
  color: #004085;
}

.status-badge.submitted {
  background: #e7f3ff;
  color: #0066cc;
}

.status-badge.not_submitted {
  background: #f1f3f4;
  color: #5f6368;
}

/* Trend Analysis Styles */
.trend-analysis-section {
  background: var(--white);
  border-radius: 12px;
  padding: 2rem;
  margin-top: 2rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid var(--border-color);
}

.trend-header {
  text-align: center;
  margin-bottom: 2rem;
}

.trend-header h4 {
  color: var(--primary-green);
  font-size: 1.4rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
}

.trend-header p {
  color: var(--text-gray);
  font-size: 1rem;
  margin: 0;
}

.trend-charts-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.trend-chart-card {
  background: var(--light-gray);
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid var(--border-color);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.trend-chart-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.trend-chart-card.full-width {
  grid-column: 1 / -1;
}

.trend-chart-card h5 {
  color: var(--primary-green);
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  text-align: center;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid var(--secondary-orange);
}

.trend-chart-wrapper {
  position: relative;
  background: var(--white);
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
  border: 1px solid var(--border-color);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.trend-chart {
  background: transparent;
  max-width: 100%;
}

.trend-insights {
  background: var(--white);
  border-radius: 8px;
  padding: 1rem;
  border: 1px solid var(--border-color);
}

.trend-insight-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: var(--light-gray);
  border-radius: 6px;
  margin-bottom: 0.5rem;
}

.trend-insight-item:last-child {
  margin-bottom: 0;
}

.trend-icon {
  font-size: 1.2rem;
  width: 24px;
  text-align: center;
}

.trend-icon.positive {
  color: #27ae60;
}

.trend-icon.negative {
  color: #e74c3c;
}

.trend-text {
  color: #2c3e50;
  font-size: 0.9rem;
  line-height: 1.4;
}

.distribution-evolution {
  background: var(--white);
  border-radius: 8px;
  padding: 1rem;
  border: 1px solid var(--border-color);
}

.distribution-insights {
  margin-top: 1.5rem;
}

.insight-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.insight-card {
  background: var(--light-gray);
  border-radius: 8px;
  padding: 1rem;
  border: 1px solid var(--border-color);
  text-align: center;
  transition: transform 0.2s ease;
}

.insight-card:hover {
  transform: translateY(-2px);
}

.insight-icon {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  display: block;
}

.insight-content h6 {
  color: var(--primary-green);
  font-size: 0.9rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
}

.insight-content p {
  color: var(--text-gray);
  font-size: 0.8rem;
  margin: 0 0 0.5rem 0;
}

.trend-indicator {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
}

.trend-indicator.positive {
  background: #d4edda;
  color: #155724;
}

.trend-indicator.negative {
  background: #f8d7da;
  color: #721c24;
}

.no-trend-data {
  text-align: center;
  padding: 3rem 1rem;
  color: var(--text-gray);
  background: var(--light-gray);
  border-radius: 8px;
  border: 1px solid var(--border-color);
}

.no-trend-data h4 {
  color: var(--primary-green);
  margin-bottom: 1rem;
}

.no-trend-data p {
  font-size: 0.9rem;
  margin: 0;
}

/* =================================================================
   RESPONSIVE DESIGN
   ================================================================= */

@media (max-width: 768px) {
  .teacher-activity-container {
    padding: 1rem;
  }
  
  .students-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .student-card {
    padding: 1.5rem;
  }
  
  .student-metrics {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .access-stats {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .main-tab-navigation, .chart-navigation {
    flex-direction: column;
    align-items: stretch;
  }
  
  .main-tab-btn, .chart-tab-btn {
    text-align: center;
    margin-bottom: 0.5rem;
    justify-content: center;
  }
  
  .breakdown-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
    text-align: left;
  }
  
  .breakdown-label {
    min-width: auto;
  }
  
  .breakdown-value {
    text-align: left;
  }
  
  .metric-stats {
    flex-direction: column;
    gap: 1rem;
  }
  
  .metric-stat {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: var(--light-gray);
    border-radius: 8px;
  }
  
  .metric-stat .stat-value {
    font-size: 1.2rem;
    margin-bottom: 0;
  }
  
  .charts-section {
    padding: 1.5rem;
  }
  
  .student-flags {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .modern-header {
    padding: 1.5rem;
  }
  
  .course-section {
    padding: 1.5rem;
  }
  
  .student-card {
    padding: 1rem;
  }
  
  .flag {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
  }
}

.charts-header {
  margin-bottom: 1.5rem;
}

.charts-header h3 {
  color: #2d5b3f;
  margin-bottom: 0.5rem;
}

.charts-header p {
  color: #666;
  margin: 0;
}

/* Quick Stats Section */
.quick-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
  padding: 1.5rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.stat-card {
  background: white;
  padding: 1rem;
  border-radius: 6px;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.stat-card .stat-number {
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: 0.25rem;
}

.stat-card .stat-number.total-students {
  color: #2d5b3f;
}

.stat-card .stat-number.at-risk {
  color: #e74c3c;
}

.stat-card .stat-number.class-average {
  color: #f4a534;
}

.stat-card .stat-number.page-views {
  color: #3d6b4f;
}

.stat-card .stat-label {
  color: #666;
  font-size: 0.9rem;
  margin: 0;
}

/* Report Preview Section */
.report-preview-section {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  margin-bottom: 1.5rem;
  border: 1px solid #e0e0e0;
}

.report-preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  border-bottom: 2px solid #2d5b3f;
  padding-bottom: 0.5rem;
}

.report-preview-header h4 {
  color: #2d5b3f;
  margin: 0;
}

.preview-actions {
  display: flex;
  gap: 0.5rem;
}

.preview-toggle-btn {
  background: #f8f9fa;
  border: 1px solid #ddd;
  padding: 0.25rem 0.75rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.85rem;
  transition: background 0.2s;
}

.preview-toggle-btn:hover {
  background: #e9ecef;
}

.report-preview-content {
  display: none;
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #eee;
  padding: 1rem;
  background: #fafafa;
  border-radius: 4px;
}

/* Report Actions Section */
.report-actions {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
}

.report-actions h4 {
  color: #2d5b3f;
  margin-bottom: 1rem;
}

.report-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
}

.report-btn {
  border: none;
  padding: 1rem;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  transition: background 0.2s;
  text-decoration: none;
  color: white;
}

.report-btn:hover {
  opacity: 0.9;
  transform: translateY(-1px);
}

.report-btn.print {
  background: #2d5b3f;
}

.report-btn.pdf {
  background: #f4b952;
}

.report-btn.csv {
  background: #2d5b3f;
}

.report-btn .btn-icon {
  font-size: 1.2em;
}

.report-btn .btn-content {
  text-align: left;
}

.report-btn .btn-title {
  font-weight: bold;
  display: block;
  margin-bottom: 0.25rem;
}

.report-btn .btn-description {
  font-size: 0.85em;
  opacity: 0.9;
}

/* Report Tips Section */
.report-tips {
  margin-top: 1rem;
  padding: 1rem;
  background: #e8f5e8;
  border-radius: 4px;
  border-left: 4px solid #27ae60;
}

.report-tips h5 {
  color: #27ae60;
  margin: 0 0 0.5rem 0;
}

.report-tips ul {
  margin: 0;
  padding-left: 1.2rem;
  color: #2c3e50;
}

.report-tips li {
  margin-bottom: 0.25rem;
}

/* Hidden Report Content */
.hidden-report-content {
  display: none;
}

.hidden-report-content h2 {
  color: #2d5b3f;
  margin-bottom: 1rem;
}

.hidden-report-content h3 {
  color: #2d5b3f;
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
}

.hidden-report-content ul {
  margin-bottom: 1rem;
}

.hidden-report-content li {
  margin-bottom: 0.5rem;
}

/* Report Table Styles */
.report-table {
  border-collapse: collapse;
  width: 100%;
  margin: 1rem 0;
}

.report-table th,
.report-table td {
  border: 1px solid #ddd;
  padding: 8px;
  text-align: left;
}

.report-table th {
  background: #f0f0f0;
  font-weight: bold;
}

.report-table tr:nth-child(even) {
  background: #f9f9f9;
}

/* CSV Data Container */
.csv-data-container {
  display: none;
  white-space: pre-line;
  font-family: monospace;
}


/* Responsive Design */
@media (max-width: 768px) {
  .quick-stats {
    grid-template-columns: 1fr;
  }
  
  .report-buttons {
    grid-template-columns: 1fr;
  }
  
  .report-preview-header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }
  
  .stat-card .stat-number {
    font-size: 1.5rem;
  }
}
/* =================================================================
   TREND ANALYSIS - MATCHING PERFORMANCE REPORT DESIGN
   ================================================================= */

/* Main Trend Analysis Container */
.trend-analysis-container {
  padding: 0;
}

/* Charts Section Header - Matching Performance Report */
.charts-header {
  text-align: center;
  margin-bottom: 2rem;
}

.charts-header h3 {
  font-size: 1.4rem;
  font-weight: 600;
  color: var(--primary-green);
  margin: 0 0 0.5rem 0;
}

.charts-header p {
  color: var(--text-gray);
  font-size: 1rem;
  margin: 0;
}

/* Trend Navigation - Matching Report Tab Style */
.trend-navigation {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-bottom: 2rem;
  background: transparent;
  padding: 0;
  border: none;
  border-bottom: 1px solid #e0e0e0;
}

.trend-tab-btn {
  background: transparent;
  border: none;
  color: #666;
  padding: 1rem 0;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  font-size: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  position: relative;
  border-bottom: 3px solid transparent;
}

.trend-tab-btn.active {
  color: var(--primary-green);
  border-bottom-color: var(--secondary-orange);
  background: transparent;
}

.trend-tab-btn:hover:not(.active) {
  color: var(--primary-green);
  background: transparent;
}

/* Tab Content */
.trend-tab-content {
  display: none;
  animation: fadeIn 0.4s ease-in;
  padding: 1.5rem;
  background: white;
}

.trend-tab-content.active {
  display: block;
}

/* Quick Stats Grid - Matching Performance Report Style */
.trend-metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
  padding: 1.5rem;
  background: var(--light-gray);
  border-radius: 8px;
}

.trend-metric-card {
  background: white;
  padding: 1rem;
  border-radius: 6px;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  border: 1px solid var(--border-color);
  transition: transform 0.2s ease;
}

.trend-metric-card:hover {
  transform: translateY(-2px);
}

.metric-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.metric-value {
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: 0.25rem;
  line-height: 1;
}

.metric-label {
  color: var(--text-gray);
  font-size: 0.9rem;
  margin: 0 0 0.5rem 0;
  font-weight: 500;
}

.metric-trend {
  font-size: 0.8rem;
  margin-top: 0.25rem;
}

/* Color variations for different metric types */
.trend-metric-card.performance .metric-value,
.trend-metric-card.engagement-high .metric-value {
  color: var(--primary-green);
}

.trend-metric-card.grade-a .metric-value,
.trend-metric-card.activity-rate .metric-value {
  color: #27ae60;
}

.trend-metric-card.grade-concern .metric-value,
.trend-metric-card.at-risk .metric-value,
.trend-metric-card.participation .metric-value {
  color: #e74c3c;
}

.trend-metric-card.submission-rate .metric-value,
.trend-metric-card.medium-risk .metric-value,
.trend-metric-card.session-length .metric-value {
  color: var(--secondary-orange);
}

.trend-metric-card.intervention .metric-value {
  color: #9b59b6;
}

.trend-metric-card.early-warning .metric-value {
  color: #1abc9c;
}

/* Trend Indicators */
.trend-indicator {
  padding: 0.2rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  display: inline-block;
}

.trend-indicator.up {
  background: #d4edda;
  color: #155724;
}

.trend-indicator.down {
  background: #f8d7da;
  color: #721c24;
}

.trend-indicator.stable {
  background: #fff3cd;
  color: #856404;
}

/* Chart Container - Matching Performance Report */
.chart-container {
  position: relative;
  background: var(--white);
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  margin-bottom: 1.5rem;
  border: 1px solid var(--border-color);
}

.chart-title {
  color: var(--primary-green);
  font-size: 1.1rem;
  font-weight: 600;
  text-align: center;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid var(--secondary-orange);
}

.trend-chart {
  max-height: 250px;
  margin-bottom: 24px;
  overflow: hidden;
}

.trend-chart canvas {
  max-height: 200px !important;
  height: 200px !important;
}

/* Performance Highlights - Matching Report Style */
.performance-highlights {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.highlight-section {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  border: 1px solid var(--border-color);
  margin-bottom: 10px;
}

.highlight-section.improving,
.highlight-section.most-engaged {
  border-left: 4px solid #27ae60;
}

.highlight-section.needs-attention,
.highlight-section.least-engaged {
  border-left: 4px solid #e74c3c;
}

.highlight-section h4 {
  color: var(--primary-green);
  margin: 0 0 1rem 0;
  font-size: 1.1rem;
  font-weight: 600;
}

.student-highlight,
.student-engagement-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid var(--light-gray);
}

.student-highlight:last-child,
.student-engagement-card:last-child {
  border-bottom: none;
}

.student-name {
  font-weight: 500;
  color: var(--primary-green);
  margin-bottom: 0.25rem;
}

.student-score {
  font-weight: 600;
  color: #3498db;
}

.engagement-stats {
  display: flex;
  gap: 1rem;
  font-size: 0.85rem;
  color: var(--text-gray);
  margin-top: 0.25rem;
}

/* Badges - Matching Report Style */
.trend-badge,
.engagement-badge {
  padding: 0.2rem 0.6rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  display: inline-block;
}

.trend-badge.excellent,
.engagement-badge.excellent {
  background: #d4edda;
  color: #155724;
}

.trend-badge.needs-help,
.engagement-badge.needs-attention {
  background: #f8d7da;
  color: #721c24;
}

/* At-Risk Students Section - Matching Report Style */
.at-risk-students-section {
  margin-bottom: 1.5rem;
}

.at-risk-students-section h4 {
  color: var(--primary-green);
  margin: 0 0 1rem 0;
  font-size: 1.1rem;
  font-weight: 600;
}

.at-risk-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.at-risk-student-card {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  border: 1px solid var(--border-color);
  border-left: 4px solid #e74c3c;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.student-info .student-name {
  font-weight: 600;
  color: var(--primary-green);
  margin-bottom: 0.5rem;
}

.student-details {
  display: flex;
  gap: 1rem;
  font-size: 0.85rem;
  color: var(--text-gray);
}

.risk-indicators {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.risk-level {
  padding: 0.2rem 0.6rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
}

.risk-level.high {
  background: #e74c3c;
  color: white;
}

.risk-factor {
  padding: 0.2rem 0.5rem;
  border-radius: 10px;
  font-size: 0.7rem;
  font-weight: 500;
  background: #f8d7da;
  color: #721c24;
}

/* Success Message - Matching Report Style */
.no-risk-message,
.positive-message {
  text-align: center;
  padding: 2rem 1rem;
  background: #e8f5e8;
  color: var(--primary-green);
  border-radius: 8px;
  border: 1px solid #c3e6c3;
}

.success-icon {
  font-size: 2rem;
  margin-bottom: 1rem;
  display: block;
}

.no-risk-message h3 {
  margin: 0 0 0.5rem 0;
  font-weight: 600;
}

.no-risk-message p,
.positive-message p {
  margin: 0;
  font-size: 0.9rem;
}

/* Engagement Distribution - Matching Report Style */
.engagement-distribution {
  margin-bottom: 1.5rem;
}

.engagement-distribution h4 {
  color: var(--primary-green);
  margin: 0 0 1rem 0;
  font-size: 1.1rem;
  font-weight: 600;
}

.distribution-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 1rem;
}

.distribution-card {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  border: 1px solid var(--border-color);
  transition: transform 0.2s ease;
}

.distribution-card:hover {
  transform: translateY(-2px);
}

.distribution-card.high {
  border-top: 4px solid #27ae60;
}

.distribution-card.medium {
  border-top: 4px solid #3498db;
}

.distribution-card.low {
  border-top: 4px solid #e74c3c;
}

.distribution-count {
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: 0.25rem;
}

.distribution-card.high .distribution-count {
  color: #27ae60;
}

.distribution-card.medium .distribution-count {
  color: #3498db;
}

.distribution-card.low .distribution-count {
  color: #e74c3c;
}

.distribution-label {
  font-weight: 600;
  color: var(--primary-green);
  margin-bottom: 0.25rem;
  font-size: 0.9rem;
}

.distribution-description {
  font-size: 0.8rem;
  color: var(--text-gray);
  margin: 0;
}

/* Responsive Design - Matching Report Style */
@media (max-width: 768px) {
  .trend-navigation {
    flex-direction: column;
    align-items: stretch;
    gap: 0;
  }
  
  .trend-tab-btn {
    text-align: center;
    margin-bottom: 0.5rem;
    justify-content: center;
    padding: 0.75rem;
  }
  
  .trend-metrics-grid {
    grid-template-columns: 1fr;
    padding: 1rem;
  }
  
  .performance-highlights,
  .engagement-highlights {
    grid-template-columns: 1fr;
  }
  
  .distribution-grid {
    grid-template-columns: 1fr;
  }
  
  .at-risk-student-card {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .trend-tab-content {
    padding: 1rem;
  }
  
  .metric-value {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .chart-container {
    padding: 1rem;
  }
  
  .highlight-section {
    padding: 1rem;
  }
  
  .at-risk-student-card {
    padding: 1rem;
  }
  
  .distribution-card {
    padding: 1rem;
  }
  
  .distribution-count {
    font-size: 1.5rem;
  }
}

/* =================================================================
   TOP PERFORMERS TABLE - ENHANCED DESIGN
   ================================================================= */

.top-performers-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  background: var(--white);
  font-size: 1rem;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(44,62,80,0.07);
}

.top-performers-table thead th {
  background: var(--light-gray);
  color: var(--primary-green);
  font-weight: 600;
  padding: 1rem 0.75rem;
  border-bottom: 2px solid var(--secondary-orange);
  text-align: left;
  font-size: 1rem;
}

.top-performers-table tbody tr {
  transition: background 0.2s;
}

.top-performers-table tbody tr:hover {
  background: #f8fafc;
}

.top-performers-table td {
  padding: 0.85rem 0.75rem;
  border-bottom: 1px solid var(--border-color);
  vertical-align: middle;
  font-size: 0.98rem;
}

.top-performers-table td:first-child {
  font-weight: bold;
  color: var(--secondary-orange);
}

.top-performers-table td .student-avatar {
  border-radius: 50%;
  border: 2px solid var(--primary-green);
  width: 36px;
  height: 36px;
  object-fit: cover;
  background: var(--light-gray);
  margin-right: 0.5rem;
  box-shadow: 0 2px 6px rgba(45,91,63,0.08);
}

.top-performers-table td .student-avatar-placeholder {
  display: inline-block;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: var(--light-gray);
  color: #bbb;
  font-size: 1.5rem;
  text-align: center;
  line-height: 36px;
  margin-right: 0.5rem;
  border: 2px solid #e9ecef;
}

.top-performers-table td .student-name {
  font-weight: 600;
  color: var(--primary-green);
  text-decoration: none;
  transition: color 0.2s;
}

.top-performers-table td .student-name:hover {
  color: var(--secondary-orange);
  text-decoration: underline;
}

.top-performers-table td {
  text-align: left;
}

.top-performers-table td:nth-child(4),
.top-performers-table td:nth-child(5),
.top-performers-table td:nth-child(6) {
  font-weight: 500;
  color: #34495e;
}

.top-performers-table th,
.top-performers-table td {
  min-width: 110px;
}

.custom-dropdown {
  appearance: none;
  background: var(--light-gray);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 0.75rem 2rem 0.75rem 1rem;
  font-size: 1rem;
  color: var(--primary-green);
  font-weight: 500;
  cursor: pointer;
  transition: border-color 0.2s;
  box-shadow: 0 2px 8px rgba(44,62,80,0.07);
  outline: none;
}

.custom-dropdown:focus {
  border-color: var(--secondary-orange);
}

.custom-dropdown option {
  color: #2c3e50;
  background: var(--white);
  font-size: 1rem;
}

@media (max-width: 900px) {
  .top-performers-table-container {
    padding: 1rem;
  }
  .top-performers-table th,
  .top-performers-table td {
    padding: 0.5rem;
    font-size: 0.95rem;
  }
}

@media (max-width: 600px) {
  .top-performers-table-container {
    padding: 0.5rem;
  }
  .top-performers-table th,
  .top-performers-table td {
    padding: 0.35rem;
    font-size: 0.9rem;
  }
}


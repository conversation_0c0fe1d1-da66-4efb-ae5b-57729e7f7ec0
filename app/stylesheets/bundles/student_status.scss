/*
 * Copyright (C) 2015 - present Instructure, Inc.
 *
 * This file is part of Canvas.
 *
 * Canvas is free software: you can redistribute it and/or modify it under
 * the terms of the GNU Affero General Public License as published by the Free
 * Software Foundation, version 3 of the License.
 *
 * Canvas is distributed in the hope that it will be useful, but WITHOUT ANY
 * WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS FOR
 * A PARTICULAR PURPOSE. See the GNU Affero General Public License for more
 * details.
 *
 * You should have received a copy of the GNU Affero General Public License along
 * with this program. If not, see <http://www.gnu.org/licenses/>.
 */
 
  .student-status-container {
    max-width: 1200px;
    margin: 0 auto;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    min-height: 100vh;
  }

  /* Header Section - Compact */
  .status-header {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    margin-bottom: 1.5rem;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
    border-left: 3px solid #2d5b3f;
  }

  .status-title {
    font-size: 1.4rem;
    font-weight: 600;
    color: #2d5b3f;
    margin: 0 0 0.5rem 0;
    line-height: 1.3;
  }

  .assignment-name {
    color: #f4a534;
    font-weight: 700;
  }

  .breadcrumb {
    color: #6c757d;
    font-size: 0.8rem;
    margin: 0;
  }

  .breadcrumb a {
    color: #2d5b3f;
    text-decoration: none;
    transition: color 0.2s;
  }

  .breadcrumb a:hover {
    color: #f4a534;
  }

  /* Overview Cards - Clickable and Interactive */
  .status-overview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
  }

  .overview-card {
    background: white;
    padding: 1rem;
    border-radius: 8px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.06);
    transition: transform 0.2s, box-shadow 0.2s;
    border-top: 3px solid;
    cursor: pointer;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    min-height: 100px;
  }

  .overview-card:hover {
    transform: translateY(-1px);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
  }

  .overview-card.filtered {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
    background: #f8f9fa;
  }

  .card-filter-indicator {
    position: absolute;
    top: 0;
    right: 0;
    width: 8px;
    height: 8px;
    background: #f4a534;
    border-radius: 50%;
    opacity: 0;
    transition: opacity 0.2s;
  }

  .overview-card.filtered .card-filter-indicator {
    opacity: 1;
  }

  .not-started-card {
    border-top-color: #dc3545;
  }

  .in-progress-card {
    border-top-color: #f4a534;
  }

  .completed-card {
    border-top-color: #28a745;
  }

  .total-card {
    border-top-color: #2d5b3f;
  }

  .card-header {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 0.5rem;
    width: 100%;
  }

  .card-header h3 {
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
    color: #2d5b3f;
    text-align: center;
  }

  .card-icon {
    font-size: 1.2rem;
    opacity: 0.7;
  }

  .overview-card p {
    margin: 0;
    color: #6c757d;
    font-weight: 500;
    font-size: 0.85rem;
    text-align: center;
  }

  /* Filter Controls */
  .filter-controls {
    background: white;
    padding: 1rem 1.5rem;
    border-radius: 8px;
    margin-bottom: 1.5rem;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.06);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
  }

  .filter-section {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;
  }

  .filter-label {
    font-weight: 600;
    color: #2d5b3f;
    font-size: 0.9rem;
  }

  .filter-buttons {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
  }

  .filter-btn {
    padding: 0.4rem 0.8rem;
    border: 1px solid #dee2e6;
    background: white;
    color: #495057;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
  }

  .filter-btn:hover {
    border-color: #2d5b3f;
    color: #2d5b3f;
  }

  .filter-btn.active {
    background: #2d5b3f;
    color: white;
    border-color: #2d5b3f;
  }

  .clear-btn {
    padding: 0.4rem 0.8rem;
    border: 1px solid #dc3545;
    background: white;
    color: #dc3545;
    border-radius: 4px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.2s;
  }

  .clear-btn:hover {
    background: #dc3545;
    color: white;
  }

  /* Table Section - Enhanced */
  .table-section {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.06);
    overflow: hidden;
  }

  .section-header {
    padding: 1rem 1.5rem;
    background: linear-gradient(135deg, #2d5b3f 0%, #1a3d2b 100%);
    color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .section-header h2 {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 600;
  }

  .results-info {
    font-size: 0.9rem;
    opacity: 0.9;
  }

  .table-wrapper {
    overflow-x: auto;
  }

  /* Custom Table Design - Enhanced */
  .students-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.85rem;
  }

  .students-table thead {
    background: #f8f9fa;
  }

  .students-table th {
    padding: 0.75rem 1rem;
    text-align: left;
    font-weight: 600;
    color: #495057;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border-bottom: 2px solid #dee2e6;
  }

  .students-table tbody tr {
    transition: background-color 0.2s;
  }

  .students-table tbody tr:hover {
    background-color: #f8f9fa;
  }

  .students-table tbody tr.hidden {
    display: none;
  }

  .students-table td {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #e9ecef;
    vertical-align: top;
  }

  /* Student Cell - Compact */
  .student-cell {
    min-width: 160px;
  }

  .student-name a {
    color: #2d5b3f;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
    display: block;
    margin-bottom: 0.2rem;
  }

  .student-name a:hover {
    color: #f4a534;
    text-decoration: underline;
  }

  .student-email {
    color: #6c757d;
    font-size: 0.75rem;
  }

  /* Status Pills - Enhanced */
  .status-pill {
    display: inline-flex;
    align-items: center;
    gap: 0.4rem;
    padding: 0.3rem 0.7rem;
    border-radius: 16px;
    font-size: 0.75rem;
    font-weight: 500;
    border: 1px solid;
  }

  .status-dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
  }

  .status-pill.not-started {
    background: #fff5f5;
    color: #dc3545;
    border-color: #dc3545;
  }

  .status-pill.not-started .status-dot {
    background: #dc3545;
  }

  .status-pill.in-progress {
    background: #fffbf0;
    color: #f4a534;
    border-color: #f4a534;
  }

  .status-pill.in-progress .status-dot {
    background: #f4a534;
  }

  .status-pill.completed {
    background: #f0fff4;
    color: #28a745;
    border-color: #28a745;
  }

  .status-pill.completed .status-dot {
    background: #28a745;
  }

  /* Grade Display - Smaller */
  .grade-display {
    font-size: 0.9rem;
  }

  .grade-score {
    font-weight: 700;
    color: #2d5b3f;
    font-size: 1rem;
  }

  .grade-total {
    color: #6c757d;
    font-weight: 500;
    margin-left: 0.2rem;
  }

  .no-grade {
    color: #adb5bd;
    font-style: italic;
  }

  /* Date Information - Compact */
  .date-info {
    display: flex;
    flex-direction: column;
    gap: 0.15rem;
  }

  .date-label {
    font-size: 0.65rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 600;
  }

  .date-info.completed .date-label {
    color: #28a745;
  }

  .date-info.in-progress .date-label {
    color: #f4a534;
  }

  .date-value {
    font-size: 0.8rem;
    color: #495057;
  }

  .no-activity {
    color: #adb5bd;
    font-style: italic;
    font-size: 0.8rem;
  }

  /* Actions Column */
  .actions-cell {
    min-width: 60px;
    text-align: center;
  }

  /* Modal Overlay */
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(2px);
  }

  /* Status Override Dropdown - Clean & Modern */
  .status-override-dropdown {
    position: relative;
    display: inline-block;
  }

  .override-trigger {
    background: none;
    border: 1px solid #dee2e6;
    padding: 0.4rem 0.6rem;
    border-radius: 4px;
    cursor: pointer;
    color: #6c757d;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 32px;
    height: 32px;
  }

  .override-trigger:hover {
    border-color: #2d5b3f;
    color: #2d5b3f;
    background: #f8f9fa;
  }

  .dots-icon {
    font-size: 1rem;
    font-weight: bold;
    line-height: 1;
  }

  .override-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    min-width: 140px;
    z-index: 100;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-5px);
    transition: all 0.2s ease;
  }

  .status-override-dropdown.active .override-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
  }

  .menu-header {
    padding: 0.6rem 0.8rem;
    border-bottom: 1px solid #e9ecef;
    font-size: 0.75rem;
    font-weight: 600;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    background: #f8f9fa;
    border-radius: 6px 6px 0 0;
  }

  .override-menu .status-option {
    width: 100%;
    padding: 0.6rem 0.8rem;
    border: none;
    background: none;
    text-align: left;
    cursor: pointer;
    transition: background-color 0.2s;
    display: flex;
    align-items: center;
    gap: 0.6rem;
    font-size: 0.85rem;
    color: #495057;
  }

  .override-menu .status-option:hover {
    background: #f8f9fa;
  }

  .override-menu .status-option:last-child {
    border-radius: 0 0 6px 6px;
  }

  .override-menu .status-option.current {
    background: #e9ecef;
    color: #6c757d;
    font-style: italic;
  }

  .status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    flex-shrink: 0;
  }

  .not-started-dot {
    background: #dc3545;
  }

  .in-progress-dot {
    background: #f4a534;
  }

  .completed-dot {
    background: #28a745;
  }

  /* Modal Updates - Cleaner Design */
  .modal-content {
    background: white;
    border-radius: 12px;
    width: 90%;
    max-width: 420px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  }

  .modal-header {
    padding: 1.5rem 1.5rem 1rem;
    border-bottom: none;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .modal-header h3 {
    margin: 0;
    color: #2d5b3f;
    font-size: 1.1rem;
    font-weight: 600;
  }

  .modal-close {
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    color: #adb5bd;
    padding: 0;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s;
  }

  .modal-close:hover {
    background: #f8f9fa;
    color: #6c757d;
  }

  .modal-body {
    padding: 0 1.5rem 1.5rem;
  }

  .change-summary {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1.5rem;
    text-align: center;
  }

  .change-summary .student-name {
    font-weight: 600;
    color: #2d5b3f;
    font-size: 1rem;
    margin-bottom: 0.8rem;
  }

  .status-change {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.8rem;
    font-size: 0.9rem;
  }

  .from-status,
  .to-status {
    padding: 0.3rem 0.8rem;
    border-radius: 16px;
    font-weight: 500;
    font-size: 0.8rem;
  }

  .arrow {
    color: #6c757d;
    font-weight: bold;
  }

  .reason-section {
    display: flex;
    flex-direction: column;
    gap: 0.6rem;
  }

  .reason-section label {
    font-weight: 500;
    color: #495057;
    font-size: 0.9rem;
  }

  .reason-section input {
    padding: 0.7rem;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    font-family: inherit;
    font-size: 0.9rem;
    transition: border-color 0.2s, box-shadow 0.2s;
  }

  .reason-section input:focus {
    outline: none;
    border-color: #2d5b3f;
    box-shadow: 0 0 0 3px rgba(45, 91, 63, 0.1);
  }

  .modal-footer {
    padding: 1rem 1.5rem 1.5rem;
    border-top: none;
    display: flex;
    justify-content: flex-end;
    gap: 0.8rem;
  }

  .btn {
    padding: 0.6rem 1.2rem;
    border: 1px solid;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    min-width: 80px;
  }

  .btn-secondary {
    background: white;
    color: #6c757d;
    border-color: #dee2e6;
  }

  .btn-secondary:hover {
    background: #f8f9fa;
    border-color: #adb5bd;
    color: #495057;
  }

  .btn-primary {
    background: #2d5b3f;
    color: white;
    border-color: #2d5b3f;
  }

  .btn-primary:hover {
    background: #1a3d2b;
    border-color: #1a3d2b;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(45, 91, 63, 0.2);
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    .student-status-container {
      padding: 1rem;
    }

    .filter-controls {
      flex-direction: column;
      align-items: stretch;
    }

    .filter-section {
      flex-direction: column;
      align-items: stretch;
      gap: 0.5rem;
    }

    .filter-buttons {
      justify-content: center;
    }

    .section-header {
      flex-direction: column;
      align-items: stretch;
      gap: 0.5rem;
    }

    .results-info {
      text-align: center;
    }

    .students-table th,
    .students-table td {
      padding: 0.5rem 0.75rem;
    }

    .action-btn {
      padding: 0.3rem 0.6rem;
      font-size: 0.7rem;
    }

    .modal-content {
      width: 95%;
      margin: 1rem;
    }
  }

  @media (max-width: 480px) {
    .status-overview-grid {
      grid-template-columns: 1fr;
    }

    .students-table {
      font-size: 0.8rem;
    }

    .filter-buttons {
      flex-direction: column;
    }

    .filter-btn {
      text-align: center;
    }
  }

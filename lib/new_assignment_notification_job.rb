# frozen_string_literal: true

#
# Copyright (C) 2025 - present Instructure, Inc.
#
# This file is part of Canvas.
#
# Canvas is free software: you can redistribute it and/or modify it under
# the terms of the GNU Affero General Public License as published by the Free
# Software Foundation, version 3 of the License.
#
# Canvas is distributed in the hope that it will be useful, but WITHOUT ANY
# WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS FOR
# A PARTICULAR PURPOSE. See the GNU Affero General Public License for more
# details.
#
# You should have received a copy of the GNU Affero General Public License along
# with this program. If not, see <http://www.gnu.org/licenses/>.

class NewAssignmentNotificationJob
  # Sends notifications to students when a new assignment is created
  def self.process(assignment)
    return unless assignment&.published?
    
    course = assignment.course
    return unless course&.available?
    
    # Check if new assignment notifications are enabled for this account
    return unless course.root_account.settings[:enable_new_assignment_notifications] != false

    # Get all active students enrolled in the course
    students = course.participating_students_by_date
    
    # Send notification to each student
    students.each do |student|
      begin
        send_new_assignment_notification_to_student(student, assignment, course)
      rescue => e
        # Log error for individual student but continue with others
        Rails.logger.error("Failed to send new assignment notification to student #{student.id}: #{e.message}")
      end
    end
  end

  def self.send_new_assignment_notification_to_student(student, assignment, course)
        return false unless student.email_channel
        
        # Check if student has opted out of new assignment notifications
        return false if student.preferences[:no_new_assignment_notifications]

        # Create a message for the student
        subject = I18n.t("New Activity: %{assignment_title}", assignment_title: assignment.title)
        
        due_date_text = assignment.due_at ? 
        I18n.t("Due: %{due_date}", due_date: format_date(assignment.due_at)) :
        I18n.t("No due date specified")
        
        # Get assignment group name
        assignment_group_name = assignment.assignment_group&.name || I18n.t("Not specified")
        
        body = I18n.t(<<~MESSAGE, 
        Dear %{student_name},

        A new assignment has been posted in your course "%{course_name}".

        Activity Title: %{assignment_title}
        Activity Type: %{assignment_group_name}
        %{due_date_text}
        Points Possible: %{points_possible}

        %{description}

        You can access the assignment here: %{assignment_url}

        This is an automated message. Please do not reply to this email.
        MESSAGE
        student_name: student.name,
        assignment_title: assignment.title,
        course_name: course.name,
        assignment_group_name: assignment_group_name,
        due_date_text: due_date_text,
        points_possible: assignment.points_possible || I18n.t("Not specified"),
        description: assignment.description.present? ? 
        I18n.t("Description: %{description}", description: strip_html_tags(assignment.description)) : 
        "",
        assignment_url: assignment_url(assignment, course))

        # Return true if the email was sent successfully
        send_notification_email(student, subject, body, course, assignment, 'new_assignment_notification_to_student')
    end

  def self.send_notification_email(user, subject, body, context, assignment, action)
    return false unless user.email_channel

    begin
      # Create a message record
      m = user.email_channel.messages.temp_record
      m.to = user.email_channel.path
      m.context = context
      m.user = user
      m.subject = subject
      m.body = body
      m.notification = Notification.create_or_find_by(name: 'new_assignment', category: 'Course Content')
      m.parse!('email')
            
      # Send the email
      message = Mailer.create_message(m)
      Mailer.deliver(message)
      
      Rails.logger.info("New assignment notification sent to user #{user.id} for assignment #{assignment.id}")
      true
    rescue => e
      Rails.logger.error("Failed to send new assignment notification to user #{user.id}: #{e.message}")
      false
    end
  end

  def self.format_date(date)
    I18n.l(date, format: :long_date_at_time)
  end

  def self.assignment_url(assignment, course)
    "#{HostUrl.protocol}://#{HostUrl.default_host}/courses/#{course.id}/assignments/#{assignment.id}"
  end

  private

  def self.strip_html_tags(html_string)
    return "" unless html_string.present?
    
    # Simple HTML tag removal
    html_string.gsub(/<[^>]*>/, '').strip
  end
end

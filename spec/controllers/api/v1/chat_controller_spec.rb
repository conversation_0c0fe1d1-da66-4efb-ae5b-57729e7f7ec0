require 'spec_helper'

describe Api::V1::<PERSON><PERSON>C<PERSON>roll<PERSON>, type: :controller do
  let!(:user) do
    u = user_model(workflow_state: 'registered')
    u.pseudonyms.create!(unique_id: '<EMAIL>', account: Account.default)
    u
  end
  let!(:other_user) do
    u = user_model(workflow_state: 'registered')
    u.pseudonyms.create!(unique_id: '<EMAIL>', account: Account.default)
    u
  end

  before do
    user_session(user)
  end

  describe 'GET #users' do
    it 'returns users with online status' do
      UserOnlineStatus.update_status(other_user.id, online: true)

      get :users

      expect(response).to be_successful
      json = JSON.parse(response.body)
      expect(json['users']).to be_an(Array)

      user_data = json['users'].find { |u| u['id'] == other_user.id }
      expect(user_data).to be_present
      expect(user_data['online']).to be true
      expect(user_data['status']).to eq 'online'
    end

    it 'excludes current user from results' do
      get :users

      json = JSON.parse(response.body)
      user_ids = json['users'].map { |u| u['id'] }
      expect(user_ids).not_to include(user.id)
    end

    it 'shows offline status for users without online status' do
      get :users

      json = JSON.parse(response.body)
      user_data = json['users'].find { |u| u['id'] == other_user.id }
      expect(user_data).to be_present
      expect(user_data['online']).to be false
      expect(user_data['status']).to eq 'offline'
    end

    it 'only includes users with registered or pre_registered workflow states' do
      # Create users with different workflow states
      registered_user = user_model(workflow_state: 'registered')
      registered_user.pseudonyms.create!(unique_id: '<EMAIL>', account: Account.default)

      pre_registered_user = user_model(workflow_state: 'pre_registered')
      pre_registered_user.pseudonyms.create!(unique_id: '<EMAIL>', account: Account.default)

      pending_approval_user = user_model(workflow_state: 'pending_approval')
      pending_approval_user.pseudonyms.create!(unique_id: '<EMAIL>', account: Account.default)

      creation_pending_user = user_model(workflow_state: 'creation_pending')
      creation_pending_user.pseudonyms.create!(unique_id: '<EMAIL>', account: Account.default)

      deleted_user = user_model(workflow_state: 'deleted')
      deleted_user.pseudonyms.create!(unique_id: '<EMAIL>', account: Account.default)

      get :users

      json = JSON.parse(response.body)
      user_ids = json['users'].map { |u| u['id'] }

      expect(user_ids).to include(registered_user.id)
      expect(user_ids).to include(pre_registered_user.id)
      expect(user_ids).not_to include(pending_approval_user.id)
      expect(user_ids).not_to include(creation_pending_user.id)
      expect(user_ids).not_to include(deleted_user.id)
    end

    it 'excludes users with deleted_at timestamp even if workflow_state is active' do
      # Create a user that has deleted_at set but workflow_state is still registered
      soft_deleted_user = user_model(workflow_state: 'registered')
      soft_deleted_user.pseudonyms.create!(unique_id: '<EMAIL>', account: Account.default)
      soft_deleted_user.update_column(:deleted_at, Time.now)

      get :users

      json = JSON.parse(response.body)
      user_ids = json['users'].map { |u| u['id'] }

      expect(user_ids).not_to include(soft_deleted_user.id)
    end

    it 'excludes users with suspended pseudonyms' do
      # Create a user with an active pseudonym, then suspend it
      suspended_user = user_model(workflow_state: 'registered')
      pseudonym = suspended_user.pseudonyms.create!(unique_id: '<EMAIL>', account: Account.default)
      pseudonym.suspend!

      get :users

      json = JSON.parse(response.body)
      user_ids = json['users'].map { |u| u['id'] }

      expect(user_ids).not_to include(suspended_user.id)
    end

    it 'excludes users with soft-deleted pseudonyms (Canvas app deletion)' do
      # Create a user with an active pseudonym, then soft-delete it (like Canvas app does)
      soft_deleted_user = user_model(workflow_state: 'registered')
      pseudonym = soft_deleted_user.pseudonyms.create!(unique_id: '<EMAIL>', account: Account.default)
      pseudonym.update!(workflow_state: 'deleted')

      get :users

      json = JSON.parse(response.body)
      user_ids = json['users'].map { |u| u['id'] }

      expect(user_ids).not_to include(soft_deleted_user.id)
    end
  end

  describe 'POST #update_status' do
    it 'updates user online status' do
      post :update_status, params: { online: true }
      
      expect(response).to be_successful
      expect(UserOnlineStatus.online?(user.id)).to be true
    end

    it 'marks user offline' do
      UserOnlineStatus.update_status(user.id, online: true)
      
      post :update_status, params: { online: false }
      
      expect(response).to be_successful
      expect(UserOnlineStatus.online?(user.id)).to be false
    end

    it 'handles JSON body from sendBeacon' do
      request.headers['Content-Type'] = 'application/json'
      
      post :update_status, body: JSON.generate({ online: false })
      
      expect(response).to be_successful
      expect(UserOnlineStatus.online?(user.id)).to be false
    end

    it 'broadcasts status change via ActionCable' do
      expect(ActionCable.server).to receive(:broadcast).with(
        'presence_channel',
        { user_id: user.id, online: true }
      )
      
      post :update_status, params: { online: true }
    end
  end

  describe 'GET #messages' do
    let!(:message1) do
      Message.create!(
        user_id: user.id,
        recipient_id: other_user.id,
        body: 'Hello',
        message_type: 'chat',
        delivered_at: Time.now
      )
    end

    let!(:message2) do
      Message.create!(
        user_id: other_user.id,
        recipient_id: user.id,
        body: 'Hi back',
        message_type: 'chat',
        delivered_at: Time.now
      )
    end

    it 'returns messages between users' do
      get :messages, params: { recipient_id: other_user.id }

      expect(response).to be_successful
      json = JSON.parse(response.body)
      expect(json['messages'].length).to eq 2
    end

    it 'marks messages from sender as seen' do
      expect(message2.seen_at).to be_nil

      get :messages, params: { recipient_id: other_user.id }

      message2.reload
      expect(message2.seen_at).not_to be_nil
    end

    it 'does not mark own messages as seen' do
      expect(message1.seen_at).to be_nil

      get :messages, params: { recipient_id: other_user.id }

      message1.reload
      expect(message1.seen_at).to be_nil
    end
  end

  describe 'POST #create' do
    it 'creates a new chat message' do
      expect {
        post :create, params: {
          message: {
            recipient_id: other_user.id,
            body: 'Test message'
          }
        }
      }.to change(Message, :count).by(1)
      
      expect(response).to be_successful
      message = Message.last
      expect(message.user_id).to eq user.id
      expect(message.recipient_id).to eq other_user.id
      expect(message.body).to eq 'Test message'
      expect(message.message_type).to eq 'chat'
    end

    it 'broadcasts message via ActionCable' do
      expect(ActionCable.server).to receive(:broadcast).twice
      
      post :create, params: {
        message: {
          recipient_id: other_user.id,
          body: 'Test message'
        }
      }
    end
  end

  describe 'POST #mark_as_read' do
    let!(:unread_message1) do
      Message.create!(
        user_id: other_user.id,
        recipient_id: user.id,
        body: 'Unread message 1',
        message_type: 'chat',
        delivered_at: Time.now
      )
    end

    let!(:unread_message2) do
      Message.create!(
        user_id: other_user.id,
        recipient_id: user.id,
        body: 'Unread message 2',
        message_type: 'chat',
        delivered_at: Time.now
      )
    end

    let!(:already_read_message) do
      Message.create!(
        user_id: other_user.id,
        recipient_id: user.id,
        body: 'Already read message',
        message_type: 'chat',
        delivered_at: Time.now,
        seen_at: 1.hour.ago
      )
    end

    let!(:message_from_different_user) do
      third_user = User.create!(name: 'Third User')
      Message.create!(
        user_id: third_user.id,
        recipient_id: user.id,
        body: 'Message from different user',
        message_type: 'chat',
        delivered_at: Time.now
      )
    end

    it 'marks unread messages from sender as read' do
      expect(unread_message1.seen_at).to be_nil
      expect(unread_message2.seen_at).to be_nil

      post :mark_as_read, params: { sender_id: other_user.id }

      expect(response).to be_successful
      json = JSON.parse(response.body)
      expect(json['success']).to be true
      expect(json['messages_marked_as_read']).to eq 2

      unread_message1.reload
      unread_message2.reload
      expect(unread_message1.seen_at).not_to be_nil
      expect(unread_message2.seen_at).not_to be_nil
    end

    it 'does not affect already read messages' do
      original_seen_at = already_read_message.seen_at

      post :mark_as_read, params: { sender_id: other_user.id }

      already_read_message.reload
      expect(already_read_message.seen_at).to eq original_seen_at
    end

    it 'does not affect messages from other users' do
      post :mark_as_read, params: { sender_id: other_user.id }

      message_from_different_user.reload
      expect(message_from_different_user.seen_at).to be_nil
    end

    it 'broadcasts read status update via ActionCable' do
      expect(ActionCable.server).to receive(:broadcast).twice

      post :mark_as_read, params: { sender_id: other_user.id }
    end

    it 'returns error when sender_id is missing' do
      post :mark_as_read, params: {}

      expect(response).to have_http_status(:bad_request)
      json = JSON.parse(response.body)
      expect(json['success']).to be false
      expect(json['error']).to eq 'Sender ID is required'
    end

    it 'returns error when sender_id is zero' do
      post :mark_as_read, params: { sender_id: 0 }

      expect(response).to have_http_status(:bad_request)
      json = JSON.parse(response.body)
      expect(json['success']).to be false
      expect(json['error']).to eq 'Sender ID is required'
    end

    it 'returns zero count when no unread messages exist' do
      # Mark all messages as read first
      Message.where(user_id: other_user.id, recipient_id: user.id).update_all(seen_at: Time.now)

      post :mark_as_read, params: { sender_id: other_user.id }

      expect(response).to be_successful
      json = JSON.parse(response.body)
      expect(json['success']).to be true
      expect(json['messages_marked_as_read']).to eq 0
    end

    it 'includes read_at timestamp in response' do
      post :mark_as_read, params: { sender_id: other_user.id }

      json = JSON.parse(response.body)
      expect(json['read_at']).to be_present
      expect { Time.iso8601(json['read_at']) }.not_to raise_error
    end
  end

  describe 'GET #unread_count' do
    let!(:unread_message1) do
      Message.create!(
        user_id: other_user.id,
        recipient_id: user.id,
        body: 'Unread message 1',
        message_type: 'chat',
        delivered_at: Time.now
      )
    end

    let!(:unread_message2) do
      Message.create!(
        user_id: other_user.id,
        recipient_id: user.id,
        body: 'Unread message 2',
        message_type: 'chat',
        delivered_at: Time.now
      )
    end

    let!(:read_message) do
      Message.create!(
        user_id: other_user.id,
        recipient_id: user.id,
        body: 'Read message',
        message_type: 'chat',
        delivered_at: Time.now,
        seen_at: Time.now
      )
    end

    it 'returns total unread count' do
      get :unread_count

      expect(response).to be_successful
      json = JSON.parse(response.body)
      expect(json['success']).to be true
      expect(json['unread_count']).to eq 2
    end

    it 'returns zero when no unread messages exist' do
      Message.where(recipient_id: user.id, message_type: 'chat').update_all(seen_at: Time.now)

      get :unread_count

      expect(response).to be_successful
      json = JSON.parse(response.body)
      expect(json['success']).to be true
      expect(json['unread_count']).to eq 0
    end
  end
end
